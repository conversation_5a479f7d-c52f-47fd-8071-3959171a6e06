"use client";

import {
  useState,
  useReducer,
  useRef,
  useLayoutEffect,
  useEffect,
} from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import conversationReducer from "./conversationReducer";
import PDFViewer from "./PDFViewer";
import micIcon from "public/mic.svg";
import micOffIcon from "public/mic-off.svg";
import unnanuLogo from "public/unnanu_logo.jpeg";
import uploadIcon from "public/upload.svg";
import bookIcon from "public/book.svg";
import pmcsLogo from "public/pmcs-logo.svg";

// Resolve backend endpoints from environment (inlined at build time for client code)
// Add safe fallbacks so we never generate relative URLs like "/upload"
const envBackend =
  process.env.BACKEND_URL || process.env.NEXT_PUBLIC_BACKEND_URL;
const envWs =
  process.env.WEBSOCKET_URL || process.env.NEXT_PUBLIC_WEBSOCKET_URL;

// Determine WebSocket protocol based on backend URL
const getWebSocketProtocol = (backendUrl) => {
  if (!backendUrl) return "wss://ivr-backend-prod.azurewebsites.net";
  const wsUrl = backendUrl.startsWith("https://") 
    ? backendUrl.replace("https://", "wss://")
    : backendUrl.replace("http://", "ws://");
  console.log("getWebSocketProtocol input:", backendUrl);
  console.log("getWebSocketProtocol output:", wsUrl);
  return wsUrl;
};

// Fallback WebSocket URL if the main one fails
const WS_FALLBACK = "wss://ivr-backend-prod.azurewebsites.net";

const BACKEND_URL = "https://ivr-backend-prod.azurewebsites.net";
const WS_BASE = getWebSocketProtocol(BACKEND_URL);

// Debug: Log the final configuration
console.log("=== WebSocket Configuration ===");
console.log("envBackend:", envBackend);
console.log("envWs:", envWs);
console.log("BACKEND_URL:", BACKEND_URL);
console.log("WS_BASE:", WS_BASE);
console.log("WS_FALLBACK:", WS_FALLBACK);
console.log("================================");

const initialConversation = {
  messages: [],
  finalTranscripts: [],
  interimTranscript: "",
  conversationId: null,
  sessionStartTime: null,
  conversationContext: null,
};

function cleanSourceText(text) {
  // Remove trailing (Page ...) or (Page - not specified...) or similar
  return text ? text.replace(/\s*\(Page[^)]*\)\s*$/, "").trim() : "";
}

// Conversation history management utilities
const CONVERSATION_STORAGE_KEY = "voice_assistant_conversations";
const CURRENT_SESSION_KEY = "voice_assistant_current_session";

function saveConversationToStorage(conversation) {
  try {
    const existingConversations = JSON.parse(
      localStorage.getItem(CONVERSATION_STORAGE_KEY) || "{}"
    );
    if (conversation.conversationId) {
      existingConversations[conversation.conversationId] = {
        ...conversation,
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem(
        CONVERSATION_STORAGE_KEY,
        JSON.stringify(existingConversations)
      );
    }
  } catch (error) {
    console.error("Error saving conversation to storage:", error);
  }
}

function loadConversationFromStorage(conversationId) {
  try {
    const existingConversations = JSON.parse(
      localStorage.getItem(CONVERSATION_STORAGE_KEY) || "{}"
    );
    return existingConversations[conversationId] || null;
  } catch (error) {
    console.error("Error loading conversation from storage:", error);
    return null;
  }
}

function getConversationHistory() {
  try {
    const existingConversations = JSON.parse(
      localStorage.getItem(CONVERSATION_STORAGE_KEY) || "{}"
    );
    return Object.values(existingConversations).sort(
      (a, b) => new Date(b.lastUpdated) - new Date(a.lastUpdated)
    );
  } catch (error) {
    console.error("Error getting conversation history:", error);
    return [];
  }
}

function clearConversationHistory() {
  try {
    localStorage.removeItem(CONVERSATION_STORAGE_KEY);
    localStorage.removeItem(CURRENT_SESSION_KEY);
  } catch (error) {
    console.error("Error clearing conversation history:", error);
  }
}

function VoiceAssistant() {
  const [conversation, dispatch] = useReducer(
    conversationReducer,
    initialConversation
  );
  const [isRunning, setIsRunning] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isReadingDocument, setIsReadingDocument] = useState(false);
  const [hasDocument, setHasDocument] = useState(false);
  const [pdfViewerVisible, setPdfViewerVisible] = useState(false);
  const [currentSourceLocation, setCurrentSourceLocation] = useState(null);
  const [conversationStarted, setConversationStarted] = useState(false);

  // Document management state
  const [currentDocument, setCurrentDocument] = useState(null);
  const [showReplaceDialog, setShowReplaceDialog] = useState(false);
  const [pendingUploadFile, setPendingUploadFile] = useState(null);
  const [showMetaModal, setShowMetaModal] = useState(false);
  const [pendingMetaFile, setPendingMetaFile] = useState(null);
  const [description, setDescription] = useState("");
  const [tag, setTag] = useState("");
  const [showUploadConfirmDialog, setShowUploadConfirmDialog] = useState(false);
  const [pendingConfirmFile, setPendingConfirmFile] = useState(null);

  // Conversation history state
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [conversationHistory, setConversationHistory] = useState([]);
  const [showWelcomeConfirmModal, setShowWelcomeConfirmModal] = useState(false);

  // Microphone permission state
  const [micStatus, setMicStatus] = useState('unknown');
  const [showMicModal, setShowMicModal] = useState(false);
  const [micPermissionError, setMicPermissionError] = useState(null);
  const [showWelcomeMicMessage, setShowWelcomeMicMessage] = useState(false);

  const wsRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioContextRef = useRef(null);
  const audioSourceRef = useRef(null);
  const outputGainRef = useRef(null);
  const cancelAudioRef = useRef(false);
  const audioCollectingRef = useRef(false);
  const audioChunkQueueRef = useRef([]);
  const audioDataRef = useRef([]);
  const activeAudioSourcesRef = useRef([]); // Track all active audio sources
  const messagesEndRef = useRef(null);
  const lyricsContainerRef = useRef(null);
  const router = useRouter();
  const [highlightedPdfUrl, setHighlightedPdfUrl] = useState(null);
  const [showHighlightedPdf, setShowHighlightedPdf] = useState(false);
  const [highlightedPdfPage, setHighlightedPdfPage] = useState(null);

  // Check microphone permission
  const [isCheckingPermission, setIsCheckingPermission] = useState(false);

  const checkMicPermission = async () => {
    if (window.ipc?.checkMicPermission) {
      setIsCheckingPermission(true);
      try {
        const result = await window.ipc.checkMicPermission();
        console.log('Mic permission check result:', result);
        setMicStatus(result.status);

        // Show welcome screen message if permission is denied and no conversation started
        if (result.status === 'denied' && !conversationStarted) {
          setShowWelcomeMicMessage(true);
        } else {
          setShowWelcomeMicMessage(false);
        }
      } catch (error) {
        console.error('Error checking mic permission:', error);
        setMicPermissionError('Failed to check microphone permission');
      } finally {
        setIsCheckingPermission(false);
      }
    }
  };

  const handleRefreshPermission = async () => {
    console.log('Manually refreshing permission status...');
    await checkMicPermission();
  };

  // Function to retry microphone access during conversation
  const retryMicrophoneAccess = async () => {
    try {
      console.log("Retrying microphone access...");
      await startMicrophone();

      // If successful, add success message to chat
      if (conversationStarted || conversation.messages.length > 0) {
        dispatch({
          type: "assistant_response",
          content: "✅ Microphone access granted! You can now use voice features.",
        });
      }

      // Clear error states
      setMicPermissionError(null);
      setShowWelcomeMicMessage(false);

    } catch (error) {
      console.error('Retry microphone access failed:', error);
      // Error handling is already done in startMicrophone()
    }
  };

  const handleMicClick = async () => {
    if (micStatus === 'granted') {
      setShowMicModal(true);
    } else if (micStatus === 'denied') {
      setShowMicModal(true);
    } else {
      if (window.ipc?.requestMicPermission) {
        const result = await window.ipc.requestMicPermission();
        if (result.granted) {
          setMicStatus('granted');
          setMicPermissionError(null);
          setShowWelcomeMicMessage(false);
        } else {
          setShowMicModal(true);
        }
      }
    }
  };

  const openMicSettings = async () => {
    if (window.ipc?.openMicSettings) {
      await window.ipc.openMicSettings();
      setShowMicModal(false);
      // Check permission after a delay to allow user to change settings
      setTimeout(async () => {
        await checkMicPermission();
        // If permission is now granted, clear error states
        if (micStatus === 'granted') {
          setMicPermissionError(null);
          setShowWelcomeMicMessage(false);
        }
      }, 1000);
    }
  };

  // Check permission on mount and window focus
  useEffect(() => {
    checkMicPermission();

    const handleFocus = () => checkMicPermission();
    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Update welcome mic message when conversation state changes
  useEffect(() => {
    if (micStatus === 'denied' && !conversationStarted) {
      setShowWelcomeMicMessage(true);
    } else {
      setShowWelcomeMicMessage(false);
    }
  }, [micStatus, conversationStarted]);

  // Load conversation history on component mount
  useEffect(() => {
    const history = getConversationHistory();
    setConversationHistory(history);

    // Try to restore current session
    const currentSession = localStorage.getItem(CURRENT_SESSION_KEY);
    if (currentSession) {
      try {
        const sessionData = JSON.parse(currentSession);
        if (sessionData.conversationId) {
          const savedConversation = loadConversationFromStorage(
            sessionData.conversationId
          );
          if (savedConversation) {
            dispatch({
              type: "load_conversation_history",
              messages: savedConversation.messages,
              conversationId: savedConversation.conversationId,
              sessionStartTime: savedConversation.sessionStartTime,
            });
            setConversationStarted(true);
            if (savedConversation.currentDocument) {
              setCurrentDocument(savedConversation.currentDocument);
              setHasDocument(true);
            }
          }
        }
      } catch (error) {
        console.error("Error restoring session:", error);
      }
    }
  }, []);

  // Save conversation to storage whenever it changes
  useEffect(() => {
    if (conversation.conversationId && conversation.messages.length > 0) {
      const conversationToSave = {
        ...conversation,
        currentDocument: currentDocument,
      };
      saveConversationToStorage(conversationToSave);

      // Save current session
      localStorage.setItem(
        CURRENT_SESSION_KEY,
        JSON.stringify({
          conversationId: conversation.conversationId,
          sessionStartTime: conversation.sessionStartTime,
        })
      );
    }
  }, [conversation, currentDocument]);

  useLayoutEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [conversation]);

  // Handle source click events - open in new window
  useEffect(() => {
    const handleSourceClick = async (event) => {
      const {
        document_name,
        page,
        start_position,
        end_position,
        document_id,
        text,
      } = event.detail;

      const sourceLocation = {
        document_name,
        page,
        start_position,
        end_position,
        document_id,
        text,
      };

      try {
        // Check if we're in Electron environment
        if (typeof window !== 'undefined' && window.ipc) {
          // Open PDF viewer in new Electron window
          await window.ipc.invoke('open-pdf-viewer', {
            documentUrl: `${BACKEND_URL}/pdf/${document_id}/${document_name}#page=${page}`,
            sourceLocation,
            documentKey: currentDocument?.name || "no-document"
          });
        } else {
          // Fallback to overlay for development
          setCurrentSourceLocation(sourceLocation);
          setPdfViewerVisible(true);
        }
      } catch (error) {
        console.error('Failed to open PDF viewer window:', error);
        // Fallback to overlay
        setCurrentSourceLocation(sourceLocation);
        setPdfViewerVisible(true);
      }
    };

    window.addEventListener("sourceClick", handleSourceClick);
    return () => {
      window.removeEventListener("sourceClick", handleSourceClick);
    };
  }, []);

  useLayoutEffect(() => {
    if (lyricsContainerRef.current) {
      const currentLine = lyricsContainerRef.current.querySelector(
        ".lyric-line.current"
      );
      if (currentLine) {
        currentLine.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }
    }
  }, [conversation.messages]);

  async function handlePdfUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Check if a document is already loaded
    if (hasDocument && currentDocument) {
      setPendingUploadFile(file);
      setShowReplaceDialog(true);
      event.target.value = null;
      return;
    }

    // Show confirmation dialog for new PDF uploads
    setPendingConfirmFile(file);
    setShowUploadConfirmDialog(true);
    event.target.value = null;
  }

  async function uploadDocument(file) {
    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", file);
    try {
      const response = await fetch(`${BACKEND_URL}/upload`, {
        method: "POST",
        body: formData,
      });
      if (response.ok) {
        const data = await response.json();
        const documentInfo = {
          name: data.filename || file.name,
          size: file.size,
          uploadTime: new Date().toISOString(),
          documentId: data.document_id,
        };

        // Clear current conversation when uploading a new document
        dispatch({ type: "reset" });
        setConversationStarted(false);

        setCurrentDocument(documentInfo);
        setHasDocument(true);
        setCurrentSourceLocation(null);

        // Start a new conversation session when document is uploaded
        dispatch({ type: "start_conversation" });

        // Automatically start WebSocket connection and microphone after PDF upload
        try {
          console.log("Automatically starting conversation after PDF upload...");
          openWebSocketConnection();
          await startMicrophone();
          await initializeAudioContext();

          setIsRunning(true);
          setIsListening(true);
          setConversationStarted(true);

          dispatch({
            type: "assistant_response",
            content: `Please start the conversation with the assistant by saying Hi Jessica.`,
          });
        } catch (err) {
          console.log("Error auto-starting conversation:", err);

          // Set conversation as started even if microphone fails, so user can see the interface
          setConversationStarted(true);

          // Handle microphone permission errors specifically
          if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
            dispatch({
              type: "assistant_response",
              content: `PDF uploaded successfully! However, microphone access is required for voice features. Please grant microphone permission by clicking the microphone icon in the header above.`,
            });
          } else {
            dispatch({
              type: "assistant_response",
              content: `PDF uploaded successfully! Click "Start Conversation" to begin asking questions about "${documentInfo.name}".`,
            });
          }
        }
      } else {
        dispatch({
          type: "assistant_response",
          content:
            "Sorry, there was an error uploading the PDF. Please try again.",
        });
      }
    } catch (error) {
      console.error("Error uploading PDF:", error);
      dispatch({
        type: "assistant_response",
        content: "Sorry, there was a network error while uploading the PDF.",
      });
    } finally {
      setIsUploading(false);
    }
  }

  function handleReplaceDocument() {
    if (pendingUploadFile) {
      // Clear current conversation when replacing document
      dispatch({ type: "reset" });
      setConversationStarted(false);
      setCurrentSourceLocation(null);

      uploadDocument(pendingUploadFile);
      setPendingUploadFile(null);
      setShowReplaceDialog(false);
    }
  }

  function handleCancelReplace() {
    setPendingUploadFile(null);
    setShowReplaceDialog(false);
  }

  function handleConfirmUpload() {
    if (pendingConfirmFile) {
      uploadDocument(pendingConfirmFile);
      setPendingConfirmFile(null);
      setShowUploadConfirmDialog(false);
    }
  }

  function handleCancelUpload() {
    setPendingConfirmFile(null);
    setShowUploadConfirmDialog(false);
  }

  function clearDocument() {
    setCurrentDocument(null);
    setHasDocument(false);
    setCurrentSourceLocation(null);
    dispatch({ type: "reset" });
  }

  function clearEverything() {
    // Clear current conversation and document
    setCurrentDocument(null);
    setHasDocument(false);
    setCurrentSourceLocation(null);
    setConversationStarted(false);
    dispatch({ type: "reset" });

    // Clear conversation history from localStorage
    clearConversationHistory();

    // Clear current session
    localStorage.removeItem(CURRENT_SESSION_KEY);

    // Close any active connections
    if (wsRef.current) {
      wsRef.current.close();
    }
    stopMicrophone();
    stopAudioPlayer();
    setIsRunning(false);
    setIsListening(false);
    setIsReadingDocument(false);
  }

  // (removed testAudio utilities and button)

  function loadConversation(conversationId) {
    const savedConversation = loadConversationFromStorage(conversationId);
    if (savedConversation) {
      dispatch({
        type: "load_conversation_history",
        messages: savedConversation.messages,
        conversationId: savedConversation.conversationId,
        sessionStartTime: savedConversation.sessionStartTime,
      });
      setConversationStarted(true);
      if (savedConversation.currentDocument) {
        setCurrentDocument(savedConversation.currentDocument);
        setHasDocument(true);
      }
      setShowHistoryModal(false);
    }
  }

  function startNewConversation() {
    dispatch({ type: "start_conversation" });
    setConversationStarted(true);
    setShowHistoryModal(false);
  }

  function openWebSocketConnection(endpoint = "/listen", retryCount = 0) {
    // Check if WebSocket is supported
    if (!window.WebSocket) {
      console.error("WebSocket is not supported in this browser");
      return;
    }
    
    // Debug: Log function call details
    console.log("=== openWebSocketConnection called ===");
    console.log("Function called with endpoint:", endpoint);
    console.log("Function called with retryCount:", retryCount);
    console.log("Current WS_BASE:", WS_BASE);
    
    // Determine which WebSocket base to use (primary or fallback)
    let wsBase = WS_BASE;
    if (retryCount === 1 && WS_BASE.startsWith('wss://')) {
      // Try HTTP/WS as fallback if WSS failed
      wsBase = WS_FALLBACK;
      console.log("Retrying with fallback WebSocket URL:", wsBase);
    }
    
    // Ensure proper URL construction without double slashes
    // Remove any trailing slash from wsBase to prevent double slashes
    const cleanWsBase = wsBase.endsWith('/') ? wsBase.slice(0, -1) : wsBase;
    const ws_url = endpoint.startsWith('/') 
      ? `${cleanWsBase}${endpoint}` 
      : `${cleanWsBase}/${endpoint}`;
    
    console.log("Opening WebSocket connection to:", ws_url);
    console.log("Original wsBase:", wsBase);
    console.log("Clean wsBase:", cleanWsBase);
    console.log("endpoint used:", endpoint);
    console.log("retryCount:", retryCount);
    
    // Log the complete WebSocket URL for debugging
    console.log("Complete WebSocket URL:", ws_url);
    console.log("URL construction: `${cleanWsBase}${endpoint}` = `${cleanWsBase}${endpoint}`");
    
    // Test if the backend is reachable first
    console.log("Testing backend connectivity...");
    
    // Simple HTTP test to check if backend is reachable
    fetch(`${BACKEND_URL}/health`)
      .then(response => {
        if (response.ok) {
          console.log("Backend is reachable via HTTP, proceeding with WebSocket...");
          createWebSocketConnection();
        } else {
          console.error("Backend responded with error:", response.status);
        }
      })
      .catch(error => {
        console.error("Backend is not reachable via HTTP:", error);
        // Still try WebSocket as it might work even if HTTP doesn't
        createWebSocketConnection();
      });
    
    function createWebSocketConnection() {
      // Prevent multiple simultaneous WebSocket connections
      if (wsRef.current && wsRef.current.readyState !== WebSocket.CLOSED) {
        console.log("WebSocket already exists and is not closed, closing existing connection...");
        wsRef.current.close();
      }
      
      // Validate WebSocket URL
      if (!ws_url || ws_url.trim() === '') {
        console.error("Invalid WebSocket URL:", ws_url);
        return;
      }
      
      try {
        console.log("Creating WebSocket connection...");
        wsRef.current = new WebSocket(ws_url);
        wsRef.current.binaryType = "arraybuffer";
      
        console.log("WebSocket created, readyState:", wsRef.current.readyState);
        
        // Add connection timeout
        const connectionTimeout = setTimeout(() => {
          if (wsRef.current && wsRef.current.readyState === WebSocket.CONNECTING) {
            console.error("WebSocket connection timeout after 10 seconds");
            wsRef.current.close();
            // Try fallback if this was the first attempt
            if (retryCount === 0) {
              console.log("Connection timeout, attempting fallback...");
              setTimeout(() => {
                openWebSocketConnection(endpoint, 1);
              }, 1000);
            }
          }
        }, 10000);
        
        wsRef.current.onopen = () => {
          clearTimeout(connectionTimeout);
          console.log("WebSocket connection opened successfully");
          console.log("WebSocket readyState:", wsRef.current.readyState);
        };
        
        wsRef.current.onmessage = (event) => {
          if (event.data instanceof ArrayBuffer) {
            // Handle PCM16 audio data from Azure TTS
            const audioData = new Uint8Array(event.data);
            console.log("Received audio data:", audioData.length, "bytes");

            // Check if audio was canceled due to barge-in
            if (cancelAudioRef.current) {
              console.log(
                "Audio chunk ignored due to cancel flag - barge-in occurred"
              );
              return;
            }

            if (audioCollectingRef.current) {
              // Double-check cancel flag before adding to queue
              if (cancelAudioRef.current) {
                console.log("Audio collection stopped due to barge-in");
                audioCollectingRef.current = false;
                audioChunkQueueRef.current = [];
                return;
              }
              audioChunkQueueRef.current.push(audioData);
            } else {
              console.log("Not collecting audio; dropping chunk");
            }
          } else {
            const message = JSON.parse(event.data);
            console.log("Received message:", message);
            if (message.type === "finish") {
              endConversation();
            } else if (message.type === "barge_in_detected") {
              console.log("Backend detected barge-in - immediately stopping audio");
              // Use enhanced barge-in functionality when backend signals barge-in
              handleBargeIn();
            } else if (message.type === "assistant_audio_started") {
              console.log("Assistant audio started");

              // Check if we're in a barge-in state
              if (cancelAudioRef.current) {
                console.log("TTS start ignored due to barge-in state");
                return;
              }

              // Reset audio state for clean new TTS
              resetAudioState();
              // Start collecting chunks for this utterance
              audioCollectingRef.current = true;
              audioChunkQueueRef.current = [];
            } else if (message.type === "assistant_audio_ended") {
              console.log("Assistant audio ended");

              // Check if audio was canceled due to barge-in
              if (cancelAudioRef.current) {
                console.log(
                  "Audio playback canceled due to barge-in - clearing chunks and skipping playback"
                );
                audioChunkQueueRef.current = [];
                audioCollectingRef.current = false;
                // Force stop any remaining audio
                forceStopAllAudio();
                return;
              }

              // Cleanup last source reference
              if (audioSourceRef.current) {
                audioSourceRef.current = null;
              }
              // Stop collecting and play the accumulated audio as a single buffer
              const chunks = audioChunkQueueRef.current;
              audioCollectingRef.current = false;
              audioChunkQueueRef.current = [];
              if (!cancelAudioRef.current && chunks.length > 0) {
                const totalLen = chunks.reduce((sum, c) => sum + c.byteLength, 0);
                const merged = new Uint8Array(totalLen);
                let offset = 0;
                for (const c of chunks) {
                  merged.set(c, offset);
                  offset += c.byteLength;
                }
                console.log("Playing merged audio of length", merged.byteLength);
                playPCM16Audio(merged);
              } else {
                console.log("No audio to play or canceled");
              }
                         } else {
               // Only dispatch transcript messages if conversation has started
               // This prevents user messages from appearing before "Hi Jessica"
               if ((message.type === "transcript_final" || message.type === "transcript_interim") && !conversationStarted) {
                 console.log(`Ignoring ${message.type} - conversation not started yet`);
                 // Don't dispatch the message to prevent UI display
               } else if (message.type === "transcript_final" || message.type === "transcript_interim") {
                 // Barge-in: on interim or final transcript, stop/mute current TTS immediately
                 console.log(
                   `Barge-in triggered by ${message.type} - stopping current audio`
                 );
                 // Use enhanced barge-in functionality
                 handleBargeIn();

                 // Also dispatch the message for UI updates
                 dispatch(message);
               } else {
                 // Handle other message types normally
                 dispatch(message);
               }
             }
          }
        };

        wsRef.current.onerror = (error) => {
          console.error("WebSocket error:", error);
          console.error("WebSocket URL attempted:", ws_url);
          console.error("WebSocket readyState:", wsRef.current?.readyState);
          
          // If this is a connection error, try to retry with fallback
          if (retryCount === 0) {
            console.log("WebSocket connection error, attempting fallback...");
            setTimeout(() => {
              if (wsRef.current && wsRef.current.readyState === WebSocket.CLOSED) {
                openWebSocketConnection(endpoint, 1);
              }
            }, 2000);
          }
        };
        
        wsRef.current.onclose = (event) => {
          console.log("WebSocket connection closed:", event.code, event.reason);
          console.log("WebSocket close wasClean:", event.wasClean);
          console.log("WebSocket close target:", event.target);
          
          // Try to retry with fallback URL if this was the first attempt
          if (retryCount === 0) {
            console.log("Attempting to retry with fallback WebSocket URL...");
            // Notify user that we're retrying
            dispatch({
              type: "assistant_response",
              content: "Connection attempt failed. Retrying with alternative server...",
            });
            // Prevent infinite recursion by checking if we're already retrying
            if (!wsRef.current || wsRef.current.readyState === WebSocket.CLOSED) {
              setTimeout(() => {
                openWebSocketConnection(endpoint, 1);
              }, 2000);
              return;
            }
          }
          
          // Only end conversation if we've exhausted retry attempts
          if (retryCount > 0) {
            console.log("WebSocket connection failed after retries, ending conversation");
            dispatch({
              type: "assistant_response",
              content: "Sorry, I'm having trouble connecting to the voice service. Please check your internet connection and try again.",
            });
            endConversation();
          }
        };
        
      } catch (error) {
        console.error("Failed to create WebSocket connection:", error);
        console.error("WebSocket URL:", ws_url);
        return;
      }
    }
  }

  function closeWebSocketConnection() {
    if (wsRef.current) {
      wsRef.current.close();
    }
  }

  async function startMicrophone() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 48000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      const audioContext = new AudioContext({ sampleRate: 48000 });
      const source = audioContext.createMediaStreamSource(stream);
      const processor = audioContext.createScriptProcessor(4096, 1, 1);

      processor.onaudioprocess = (e) => {
        if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
          const inputData = e.inputBuffer.getChannelData(0);
          // Convert Float32Array to ArrayBuffer for WebSocket transmission
          const buffer = inputData.buffer.slice(
            inputData.byteOffset,
            inputData.byteOffset + inputData.byteLength
          );
          wsRef.current.send(buffer);
        }
      };

      source.connect(processor);
      processor.connect(audioContext.destination);

      // Store references for cleanup
      mediaRecorderRef.current = { stream, audioContext, source, processor };

      // Clear any previous permission errors
      setMicPermissionError(null);
      setShowWelcomeMicMessage(false);

    } catch (error) {
      console.error('Error starting microphone:', error);
      console.log('Conversation state:', { conversationStarted, messageCount: conversation.messages.length });

      // Handle different types of microphone errors
      if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
        setMicStatus('denied');
        setMicPermissionError('Microphone access denied. Please grant permission to use voice features.');

        // Always add error message to chat if conversation UI is visible
        // Check both conversationStarted and if we have any messages (indicating chat is visible)
        if (conversationStarted || conversation.messages.length > 0) {
          console.log('Adding microphone error message to chat');
          // Add error message to chat during conversation
          dispatch({
            type: "assistant_response",
            content: "🎤 Microphone access is required to use IVR. Please grant microphone permission by clicking the microphone icon in the header above, or check your system settings.",
            microphoneError: true, // Special flag to show retry button
          });
        } else {
          console.log('Showing welcome screen microphone message');
          // Show welcome screen message
          setShowWelcomeMicMessage(true);
        }
      } else if (error.name === 'NotFoundError') {
        setMicPermissionError('No microphone found. Please connect a microphone and try again.');
        // Always show microphone errors in chat if conversation is active
        if (conversationStarted || conversation.messages.length > 0) {
          dispatch({
            type: "assistant_response",
            content: "🎤 No microphone detected. Please connect a microphone and try again.",
            microphoneError: true,
          });
        }
      } else {
        setMicPermissionError('Failed to access microphone. Please check your device settings.');
        // Always show microphone errors in chat if conversation is active
        if (conversationStarted || conversation.messages.length > 0) {
          dispatch({
            type: "assistant_response",
            content: "🎤 Failed to access microphone. Please check your device settings and try again.",
            microphoneError: true,
          });
        }
      }

      // Re-check permission status
      await checkMicPermission();

      // Re-throw error to be handled by calling function
      throw error;
    }
  }

  function stopMicrophone() {
    if (mediaRecorderRef.current) {
      if (mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream
          .getTracks()
          .forEach((track) => track.stop());
      }
      if (
        mediaRecorderRef.current.audioContext &&
        mediaRecorderRef.current.audioContext.state !== "closed"
      ) {
        try {
          mediaRecorderRef.current.audioContext.close();
        } catch (error) {
          console.log("AudioContext already closed or closing");
        }
      }
      if (mediaRecorderRef.current.processor) {
        mediaRecorderRef.current.processor.disconnect();
      }
      if (mediaRecorderRef.current.source) {
        mediaRecorderRef.current.source.disconnect();
      }
      mediaRecorderRef.current = null;
    }
  }

  async function initializeAudioContext() {
    try {
      // Create AudioContext for raw PCM16 playback
      const audioContext = new (window.AudioContext ||
        window.webkitAudioContext)({ sampleRate: 16000 });

      // Store references for cleanup
      audioContextRef.current = audioContext;
      // Create a master gain node to allow instant mute for barge-in
      const gainNode = audioContext.createGain();
      gainNode.gain.setValueAtTime(1.0, audioContext.currentTime);
      gainNode.connect(audioContext.destination);
      outputGainRef.current = gainNode;

      console.log("Audio context created, state:", audioContext.state);

      // Resume audio context if it's suspended (browser requirement)
      if (audioContext.state === "suspended") {
        console.log("Audio context suspended, attempting to resume...");
        await audioContext.resume();
        console.log("Audio context resumed, new state:", audioContext.state);
      }

      // Test audio context with a small silent buffer
      const testBuffer = audioContext.createBuffer(1, 100, 16000);
      const testSource = audioContext.createBufferSource();
      testSource.buffer = testBuffer;
      testSource.connect(outputGainRef.current);
      testSource.start();
      testSource.stop();

      console.log("Audio context test completed successfully");

      // No audible beep on initialization to avoid user-disruptive sound
    } catch (error) {
      console.error("Error initializing audio context:", error);
      throw error;
    }
  }

  function startAudioPlayer() {
    // This function is now deprecated, use initializeAudioContext instead
    console.log("startAudioPlayer called - use initializeAudioContext instead");
  }

  function playPCM16Audio(audioData) {
    console.log("playPCM16Audio called with", audioData.length, "bytes");

    // Check if audio was canceled due to barge-in
    if (cancelAudioRef.current) {
      console.log(
        "Audio playback canceled due to barge-in - skipping playback"
      );
      return;
    }

    if (!audioContextRef.current) {
      console.error("Audio context not initialized");
      return;
    }

    console.log("Audio context state:", audioContextRef.current.state);

    // Add a simple test to verify audio context is working
    if (audioData.length === 0) {
      console.log("Empty audio data received");
      return;
    }

    // CRITICAL: Stop ALL existing audio sources before playing new one
    console.log("Stopping all existing audio sources before playing new audio");
    stopAllActiveAudioSources();

    // Check if audio context is running
    if (audioContextRef.current.state !== "running") {
      console.log("Audio context not running, attempting to resume...");
      audioContextRef.current
        .resume()
        .then(() => {
          console.log(
            "Audio context resumed successfully, retrying playback..."
          );
          // Retry playing the audio after resume
          setTimeout(() => playPCM16Audio(audioData), 100);
        })
        .catch((error) => {
          console.error("Failed to resume audio context:", error);
        });
      return;
    }

    try {
      // Convert Uint8Array to Int16Array (PCM16)
      const int16Array = new Int16Array(
        audioData.buffer,
        audioData.byteOffset,
        audioData.byteLength / 2
      );
      console.log("Converted to Int16Array:", int16Array.length, "samples");

      // Convert Int16Array to Float32Array for Web Audio API
      const float32Array = new Float32Array(int16Array.length);
      for (let i = 0; i < int16Array.length; i++) {
        float32Array[i] = int16Array[i] / 32768.0; // Convert from 16-bit to float
      }
      console.log("Converted to Float32Array:", float32Array.length, "samples");

      // Check audio levels (use a more efficient method for large arrays)
      let maxLevel = 0;
      for (let i = 0; i < float32Array.length; i++) {
        const absValue = Math.abs(float32Array[i]);
        if (absValue > maxLevel) {
          maxLevel = absValue;
        }
      }
      console.log("Audio max level:", maxLevel);

      // Create audio buffer and play
      const audioBuffer = audioContextRef.current.createBuffer(
        1,
        float32Array.length,
        16000
      );
      audioBuffer.getChannelData(0).set(float32Array);

      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      // Route through master gain for controllable mute
      if (outputGainRef.current) {
        source.connect(outputGainRef.current);
      } else {
        source.connect(audioContextRef.current.destination);
      }

      // Add event listeners for debugging and cleanup
      source.onended = () => {
        console.log("Audio source ended");
        // Remove from active sources when ended
        const index = activeAudioSourcesRef.current.indexOf(source);
        if (index > -1) {
          activeAudioSourcesRef.current.splice(index, 1);
        }
      };
      source.onerror = (error) => {
        console.error("Audio source error:", error);
        // Remove from active sources on error
        const index = activeAudioSourcesRef.current.indexOf(source);
        if (index > -1) {
          activeAudioSourcesRef.current.splice(index, 1);
        }
      };

      // Add to active sources tracking
      activeAudioSourcesRef.current.push(source);
      
      source.start();
      audioSourceRef.current = source;

      console.log(
        `Playing PCM16 audio: ${float32Array.length} samples, duration: ${
          float32Array.length / 16000
        }s`
      );
    } catch (error) {
      console.error("Error playing PCM16 audio:", error);
    }
  }

  function stopAllActiveAudioSources() {
    console.log("Stopping all active audio sources:", activeAudioSourcesRef.current.length);
    
    // Stop all tracked audio sources
    activeAudioSourcesRef.current.forEach((source, index) => {
      try {
        if (source && typeof source.stop === 'function') {
          source.stop(0);
          console.log(`Stopped audio source ${index}`);
        }
      } catch (e) {
        console.log(`Error stopping audio source ${index}:`, e);
      }
    });
    
    // Clear the array
    activeAudioSourcesRef.current = [];
    
    // Also clear the main audio source reference
    if (audioSourceRef.current) {
      try {
        audioSourceRef.current.stop(0);
      } catch (e) {
        console.log("Error stopping main audio source:", e);
      }
      audioSourceRef.current = null;
    }
    
    console.log("All audio sources stopped");
  }

  function isAudioPlaying() {
    // Consider audio playing if we have an active source and context is running
    return !!(
      audioContextRef.current &&
      audioContextRef.current.state === "running" &&
      (audioSourceRef.current || activeAudioSourcesRef.current.length > 0)
    );
  }

  function skipCurrentAudio() {
    console.log("Skip audio requested - performing barge-in");

    // Set cancel flag to prevent new audio from playing
    cancelAudioRef.current = true;

    // Stop ALL active audio sources immediately
    stopAllActiveAudioSources();

    // Clear any buffered audio chunks IMMEDIATELY
    audioChunkQueueRef.current = [];
    audioCollectingRef.current = false;

    // Clear any pending audio data
    if (audioDataRef.current) {
      audioDataRef.current = [];
      console.log("Audio data buffer cleared");
    }

    // Instantly mute via gain node
    if (outputGainRef.current && audioContextRef.current) {
      try {
        outputGainRef.current.gain.cancelScheduledValues(0);
        outputGainRef.current.gain.setValueAtTime(
          0.0,
          audioContextRef.current.currentTime
        );
        console.log("Audio muted via gain node");
      } catch (e) {
        console.log("Gain mute error:", e);
      }
    }

    // Force stop any ongoing audio processing
    if (
      audioContextRef.current &&
      audioContextRef.current.state === "running"
    ) {
      try {
        // Suspend the audio context temporarily to stop all audio
        audioContextRef.current.suspend().then(() => {
          console.log("Audio context suspended for barge-in");
          // Resume after a short delay
          setTimeout(() => {
            if (
              audioContextRef.current &&
              audioContextRef.current.state === "suspended"
            ) {
              audioContextRef.current.resume();
              console.log("Audio context resumed after barge-in");
            }
          }, 50);
        });
      } catch (e) {
        console.log("Audio context suspend error:", e);
      }
    }

    // Reset cancel flag after a short delay to allow new TTS to start
    setTimeout(() => {
      if (cancelAudioRef.current) {
        console.log("Resetting cancel flag for new TTS");
        cancelAudioRef.current = false;
      }
    }, 200);

    console.log("Barge-in completed - all audio stopped and cleared");
  }

  function stopAudioPlayer() {
    // Stop all active audio sources first
    stopAllActiveAudioSources();
    
    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }
    audioDataRef.current = [];
  }

  function forceStopAllAudio() {
    console.log("Force stopping all audio - emergency stop");

    // Set cancel flag
    cancelAudioRef.current = true;

    // Stop ALL active audio sources
    stopAllActiveAudioSources();

    // Clear all buffers
    audioChunkQueueRef.current = [];
    audioDataRef.current = [];
    audioCollectingRef.current = false;

    // Mute gain
    if (outputGainRef.current && audioContextRef.current) {
      try {
        outputGainRef.current.gain.cancelScheduledValues(0);
        outputGainRef.current.gain.setValueAtTime(
          0.0,
          audioContextRef.current.currentTime
        );
      } catch (e) {
        console.log("Force stop gain error:", e);
      }
    }

    // Suspend audio context
    if (
      audioContextRef.current &&
      audioContextRef.current.state === "running"
    ) {
      try {
        audioContextRef.current.suspend();
        console.log("Audio context force suspended");
      } catch (e) {
        console.log("Force suspend error:", e);
      }
    }

    // Reset cancel flag after a delay to allow new TTS
    setTimeout(() => {
      if (cancelAudioRef.current) {
        console.log("Resetting cancel flag after force stop");
        cancelAudioRef.current = false;
      }
    }, 300);

    console.log("All audio force stopped");
  }

  async function startConversation() {
    // Start a new conversation session if not already started
    if (!conversation.conversationId) {
      dispatch({ type: "start_conversation" });
    }

    setConversationStarted(true);
    try {
      openWebSocketConnection();
      await startMicrophone();

      // Initialize audio context with user interaction
      console.log("Initializing audio context...");
      await initializeAudioContext();
      console.log("Audio context initialized successfully");

      setIsRunning(true);
      setIsListening(true);
    } catch (err) {
      console.log("Error starting conversation:", err);

      // If it's a microphone permission error, don't end conversation completely
      // Just keep the conversation UI open so user can see the error message
      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        setIsRunning(false);
        setIsListening(false);
        // Keep conversationStarted true so user can see the chat with error message
      } else {
        endConversation();
      }
    }
  }

  async function startDocumentReading() {
    if (!hasDocument) {
      dispatch({
        type: "error",
        content:
          "Please upload a PDF document first before starting document reading.",
      });
      return;
    }

    // Start a new conversation session if not already started
    if (!conversation.conversationId) {
      dispatch({ type: "start_conversation" });
    }

    setConversationStarted(true);
    try {
      openWebSocketConnection("/document-reader");
      await startMicrophone();

      // Initialize audio context with user interaction
      await initializeAudioContext();

      setIsRunning(true);
      setIsListening(true);
      setIsReadingDocument(true);
    } catch (err) {
      console.log("Error starting document reading:", err);

      // If it's a microphone permission error, don't end conversation completely
      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        setIsRunning(false);
        setIsListening(false);
        setIsReadingDocument(false);
        // Keep conversationStarted true so user can see the chat with error message
      } else {
        endConversation();
      }
    }
  }

  function endConversation() {
    closeWebSocketConnection();
    stopMicrophone();
    stopAudioPlayer();
    setIsRunning(false);
    setIsListening(false);
    setIsReadingDocument(false);
    setConversationStarted(false);
  }

  function toggleListening() {
    if (isListening) {
      mediaRecorderRef.current.pause();
    } else {
      mediaRecorderRef.current.resume();
    }
    setIsListening(!isListening);
  }

  const currentTranscript = conversation.interimTranscript;

  // Latest features for display
  const features = [
    {
      icon: micIcon,
      title: "Voice Conversations",
      description:
        "Have natural voice conversations with AI. Ask questions about your documents and get instant voice responses with human-like speech synthesis.",
    },
    // {
    //   icon: bookIcon,
    //   title: "Precise Source Highlighting",
    //   description: "See exactly where answers come from with intelligent text highlighting. The system finds and highlights the exact source text in your PDF documents."
    // }
  ];

  async function fetchHighlightedPDF() {
    if (!currentDocument || !currentSourceLocation) return;
    const res = await fetch(`${BACKEND_URL}/api/highlight_pdf`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        pdf_filename: currentDocument.name,
        text: currentSourceLocation.text,
        page: currentSourceLocation.page,
      }),
    });
    if (!res.ok) {
      alert("Failed to get highlighted PDF");
      return;
    }
    const data = await res.json();
    const highlightedUrl = `${BACKEND_URL}${data.url}`;
    setHighlightedPdfUrl(highlightedUrl);
    setShowHighlightedPdf(true);
  }

  // Automatically fetch and show highlighted PDF when PDF viewer is opened for a source
  useEffect(() => {
    async function fetchAndShowHighlightedPDF() {
      if (!pdfViewerVisible || !currentSourceLocation || !currentDocument)
        return;
      const cleanedText = cleanSourceText(currentSourceLocation.text);
      const res = await fetch(`${BACKEND_URL}/api/highlight_pdf`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          pdf_filename: currentDocument.name,
          text: cleanedText,
          page: currentSourceLocation.page,
        }),
      });
      if (!res.ok) {
        setHighlightedPdfUrl(null);
        setShowHighlightedPdf(false);
        setHighlightedPdfPage(null);
        return;
      }
      const data = await res.json();
      const highlightedUrl = `${BACKEND_URL}${data.url}`;
      setHighlightedPdfUrl(highlightedUrl);
      setShowHighlightedPdf(true);
      setHighlightedPdfPage(data.found_page || currentSourceLocation.page);
    }
    fetchAndShowHighlightedPDF();
  }, [pdfViewerVisible, currentSourceLocation, currentDocument]);

  // Helper to get the latest finalSource from conversation.messages
  const getLatestFinalSource = () => {
    for (let i = conversation.messages.length - 1; i >= 0; i--) {
      const msg = conversation.messages[i];
      if (msg.sources && Array.isArray(msg.sources) && msg.sources.length > 0) {
        return msg.sources[0];
      }
    }
    return null;
  };

  const latestFinalSource = getLatestFinalSource();

  function resetAudioState() {
    console.log("Resetting audio state for new TTS");

    // Clear cancel flag
    cancelAudioRef.current = false;

    // Stop all existing audio sources
    stopAllActiveAudioSources();

    // Clear buffers
    audioChunkQueueRef.current = [];
    audioDataRef.current = [];

    // Ensure gain is up for new audio
    if (outputGainRef.current && audioContextRef.current) {
      try {
        outputGainRef.current.gain.cancelScheduledValues(0);
        outputGainRef.current.gain.setValueAtTime(
          1.0,
          audioContextRef.current.currentTime
        );
        console.log("Audio gain reset to normal level");
      } catch (e) {
        console.log("Gain reset error:", e);
      }
    }

    console.log("Audio state reset completed");
  }

  // Enhanced barge-in functionality
  function handleBargeIn() {
    console.log("Barge-in detected - immediately stopping all audio");
    
    // Set cancel flag to prevent new audio from playing
    cancelAudioRef.current = true;
    
    // Stop ALL active audio sources immediately
    stopAllActiveAudioSources();
    
    // Clear any buffered audio chunks IMMEDIATELY
    audioChunkQueueRef.current = [];
    audioCollectingRef.current = false;
    
    // Clear any pending audio data
    if (audioDataRef.current) {
      audioDataRef.current = [];
      console.log("Audio data buffer cleared");
    }
    
    // Instantly mute via gain node for immediate silence
    if (outputGainRef.current && audioContextRef.current) {
      try {
        outputGainRef.current.gain.cancelScheduledValues(0);
        outputGainRef.current.gain.setValueAtTime(
          0.0,
          audioContextRef.current.currentTime
        );
        console.log("Audio muted via gain node for immediate silence");
      } catch (e) {
        console.log("Gain mute error:", e);
      }
    }
    
    // Force stop any ongoing audio processing
    if (audioContextRef.current && audioContextRef.current.state === "running") {
      try {
        // Suspend the audio context temporarily to stop all audio
        audioContextRef.current.suspend().then(() => {
          console.log("Audio context suspended for barge-in");
          // Resume after a short delay to allow new audio
          setTimeout(() => {
            if (audioContextRef.current && audioContextRef.current.state === "suspended") {
              audioContextRef.current.resume();
              console.log("Audio context resumed after barge-in");
            }
          }, 50);
        });
      } catch (e) {
        console.log("Audio context suspend error:", e);
      }
    }
    
    // Reset cancel flag after a short delay to allow new TTS to start
    setTimeout(() => {
      if (cancelAudioRef.current) {
        console.log("Resetting cancel flag for new TTS");
        cancelAudioRef.current = false;
      }
    }, 200);
    
    console.log("Barge-in completed - all audio stopped and cleared");
  }

  return (
    <div className="flex flex-col h-screen overflow-hidden bg-gray-900 text-white">
      <header className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900">
        <button
          onClick={() => setShowWelcomeConfirmModal(true)}
          className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          Back to Welcome
        </button>

        {/* PMCS Services Logo - Center */}
        <div className="flex items-center justify-center flex-1">
          <Image
            src="https://pmcsservices.com/assets/img/pmcs-logo.png"
            width={100}
            height={50}
            alt="PMCS Services Logo"
          />
        </div>

        {/* Right header */}
        <div className="flex items-center gap-4">
          {/* Microphone controls */}
          <div className="flex items-center gap-2">
            <button
              onClick={handleMicClick}
              className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-800 transition-colors"
              title={micStatus === 'granted' ? 'Microphone granted' : micStatus === 'denied' ? 'Microphone denied - Click to allow' : 'Check microphone access'}
            >
              <svg
                className={`w-5 h-5 ${micStatus === 'granted' ? 'text-green-400' : micStatus === 'denied' ? 'text-red-400' : 'text-yellow-400'}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                />
              </svg>
              <span className={`text-sm ${micStatus === 'granted' ? 'text-green-400' : micStatus === 'denied' ? 'text-red-400' : 'text-yellow-400'}`}>
                {micStatus === 'granted' ? 'Granted' : micStatus === 'denied' ? 'Denied' : 'Mic'}
              </span>
            </button>

            {/* Refresh button */}
            <button
              onClick={handleRefreshPermission}
              disabled={isCheckingPermission}
              className="p-2 rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
              title="Refresh permission status"
            >
              <svg
                className={`w-4 h-4 text-gray-400 hover:text-white ${isCheckingPermission ? 'animate-spin' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </button>
          </div>

          <div className="flex items-center gap-2 text-gray-300">
            <span className="text-sm">Powered by</span>
            <Image
              src={unnanuLogo}
              width={24}
              height={24}
              alt="Unnanu AI Logo"
              className="rounded-full"
            />
          </div>
        </div>
      </header>

      {/* Current Document Banner */}
      {currentDocument && (
        <div className="bg-blue-900/50 border-b border-blue-700/50 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <svg
                  className="w-4 h-4 text-blue-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <div>
                <p className="text-blue-200 font-medium">
                  {currentDocument.name}
                </p>
                <p className="text-blue-300/70 text-sm">
                  {(currentDocument.size / 1024 / 1024).toFixed(2)} MB •
                  Uploaded{" "}
                  {new Date(currentDocument.uploadTime).toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="text-blue-300/70 text-sm">
              {conversation.messages.filter((m) => m.role === "user").length}{" "}
              questions asked
              {conversation.conversationId && (
                <span className="ml-2">
                  • Session: {conversation.conversationId.slice(-8)}
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      <main className="flex-1 overflow-auto bg-gray-900 flex flex-col">
        {conversation.messages.length === 0 ? (
          conversationStarted ? (
            // Ask question screen when conversation has started but no messages yet
            <div className="flex flex-col items-center justify-center h-full text-center p-6">
              <div className="max-w-2xl mx-auto">
                <div className="flex items-center justify-center">
                  <div className="bg-gray-800/50 backdrop-blur-sm rounded-3xl p-12 border border-gray-700/50">
                    <div className="flex flex-col items-center space-y-8">
                      {/* Microphone Icon */}
                      <div className="w-32 h-32 bg-blue-500/20 rounded-full flex items-center justify-center border-4 border-blue-400/50">
                        <Image
                          src={micIcon}
                          width={64}
                          height={64}
                          alt="Microphone"
                          className="text-blue-400"
                        />
                      </div>

                      <div className="space-y-4">
                        <h2 className="text-4xl font-bold text-white mb-4">
                          Ask Any Question
                        </h2>

                        <p className="text-gray-300 text-lg leading-relaxed max-w-md">
                          Your document is ready! Say &quot;Hi Jessica&quot; to start the conversation, then ask any question regarding the document and I&apos;ll help you find the information you need.
                        </p>
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          <span>Voice recognition active</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          <span>Ready to listen</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Large upload area when no conversation has started
            <div className="flex flex-col items-center justify-center py-12 px-6">
              <div className="text-center mb-10">
                <h2 className="text-4xl font-bold text-white mb-4">
                Hello. This is Jessica, Your AI Voice Assistant. <br/>Please Upload Your PDF Document To Get Insights.
                </h2>
                <p className="text-gray-300 text-lg max-w-xl mx-auto">
                  Say &quot;Hi Jessica&quot; to start the conversation.
                </p>
              </div>

              {/* Microphone Permission Message on Welcome Screen */}
              {showWelcomeMicMessage && (
                <div className="mb-8 max-w-2xl mx-auto">
                  <div className="bg-yellow-900/30 border border-yellow-600/30 rounded-lg p-6">
                    <div className="flex items-center justify-center mb-4">
                      <svg className="w-8 h-8 text-yellow-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                      <span className="text-yellow-300 font-bold text-xl">Microphone Access Required</span>
                    </div>
                    <p className="text-yellow-200 text-lg text-center leading-relaxed mb-4">
                      Microphone access is required to use IVR. Please grant microphone permission to enable voice features.
                    </p>
                    <div className="flex justify-center gap-4">
                      <button
                        onClick={openMicSettings}
                        className="px-6 py-3 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-semibold transition-colors"
                      >
                        Open Settings
                      </button>
                      <button
                        onClick={handleRefreshPermission}
                        disabled={isCheckingPermission}
                        className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold transition-colors disabled:opacity-50"
                      >
                        {isCheckingPermission ? 'Checking...' : 'Refresh Status'}
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              <label className="cursor-pointer group">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-600/90 hover:to-blue-700/90 rounded-full flex items-center justify-center transition-all duration-500 shadow-2xl hover:shadow-blue-500/25 border-4 border-blue-400/20 hover:border-blue-400/40 transform hover:scale-105 backdrop-blur-md">
                  <Image
                    src={uploadIcon}
                    width={46}
                    height={46}
                    alt="Upload Document"
                    className="text-white transition-transform duration-500 group-hover:scale-110"
                  />
                </div>
                <input
                  type="file"
                  accept=".pdf"
                  onChange={handlePdfUpload}
                  className="hidden"
                />
              </label>
              

            </div>
          )
        ) : (
          // Regular conversation messages
          <div className="flex-1 overflow-y-auto px-4">
            <div className="flex flex-col items-start space-y-4">
            {conversation.messages.map(
              (
                {
                  role,
                  content,
                  type,
                  progress,
                  lines,
                  current_line_index,
                  section,
                  total_lines,
                  source,
                  source_location,
                  timestamp,
                  messageId,
                  microphoneError,
                },
                idx
              ) => {
                // DEBUG: Log the message object to see if state is updating
                if (type === "reading_lyrics") {
                  console.log("Lyric Message State:", {
                    current_line_index,
                    section,
                    total_lines,
                  });
                }

                if (type === "reading_lyrics" && lines) {
                  const getLineStyle = (lineIndex) => {
                    let style = {
                      fontWeight: 600,
                      fontSize: "1.5rem",
                      lineHeight: 1.5,
                      padding: "0.5rem",
                      margin: "0.25rem 0",
                      transition: "all 0.3s ease-in-out",
                      textAlign: "center",
                    };
                    if (lineIndex === current_line_index) {
                      style.color = "#FFFFFF"; // White for current
                      style.transform = "scale(1.05)";
                    } else if (lineIndex < current_line_index) {
                      style.color = "#6B7280"; // Gray for completed
                      style.opacity = 0.8;
                      style.transform = "scale(0.95)";
                    } else {
                      style.color = "#4B5563"; // Darker gray for upcoming
                      style.opacity = 0.7;
                      style.transform = "scale(0.95)";
                    }
                    return style;
                  };

                  return (
                    <div key={messageId || idx} className="w-full">
                      <div className="reading-content-lyrics">
                        <div
                          style={{
                            color: "#60a5fa",
                            marginBottom: "1rem",
                            textAlign: "center",
                          }}
                        >
                          <div className="text-sm text-blue-300 mb-4 text-center">
                            Section {section} - Line {current_line_index + 1} of{" "}
                            {total_lines}
                          </div>
                        </div>
                        <div
                          className="lyrics-container"
                          ref={lyricsContainerRef}
                        >
                          {lines.map((line, lineIdx) => (
                            <p
                              key={lineIdx}
                              className={`lyric-line ${
                                lineIdx === current_line_index
                                  ? "current"
                                  : lineIdx < current_line_index
                                  ? "completed"
                                  : "upcoming"
                              }`}
                              style={getLineStyle(lineIdx)}
                            >
                              {line}
                            </p>
                          ))}
                        </div>
                      </div>
                    </div>
                  );
                }

                if (type === "pdf_answer") {
                  const handleSourceClick = (sourceLocation) => {
                    if (sourceLocation) {
                      // Create a custom event to notify the parent component
                      const event = new CustomEvent("sourceClick", {
                        detail: {
                          document_name: sourceLocation.document_name,
                          page: sourceLocation.page,
                          start_position: sourceLocation.start_position,
                          end_position: sourceLocation.end_position,
                          document_id: sourceLocation.document_id,
                          text: sourceLocation.text,
                        },
                      });
                      window.dispatchEvent(event);
                    }
                  };

                  // Show only the final source (first in sources array)
                  const sources = conversation.messages[idx].sources;
                  const finalSource =
                    Array.isArray(sources) && sources.length > 0
                      ? sources[0]
                      : null;

                  return (
                    <div
                      key={messageId || idx}
                      className={`w-full flex justify-start`}
                    >
                      <div className="p-4 rounded-lg max-w-lg bg-gray-700">
                        <p className="mb-4 text-lg text-white">{content}</p>
                        {finalSource && (
                          <div className="mb-2">
                            <blockquote className="bg-blue-50 border-l-4 border-blue-500 pl-4 pr-4 py-3 text-gray-900 text-base italic rounded shadow-sm mb-1">
                              {cleanSourceText(finalSource.text)}
                            </blockquote>
                            {finalSource.page && (
                              <div className="mb-2 text-xs text-blue-200 font-semibold">
                                Page {finalSource.page}
                              </div>
                            )}
                          </div>
                        )}
                        {source_location && (
                          <button
                            onClick={() => handleSourceClick(source_location)}
                            className="mt-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg font-semibold shadow transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400"
                            title={`View this source on page ${source_location.page} in the PDF`}
                          >
                            <span
                              role="img"
                              aria-label="View in PDF"
                              className="mr-2"
                            >
                              📄
                            </span>
                            View in PDF (Page {source_location.page})
                          </button>
                        )}
                      </div>
                    </div>
                  );
                }

                return (
                  <div
                    key={messageId || idx}
                    className={`w-full flex ${
                      role === "user" ? "justify-end" : 
                      (role === "assistant" && (content.includes("PDF") && content.includes("uploaded successfully") || content.includes("Hi, I'm here to help") || content.includes("Please start the conversation with the assistant by saying Hi Jessica"))) ? "justify-center" : "justify-start"
                    }`}
                  >
                    <div
                      className={`p-4 rounded-lg ${
                        role === "user" ? "bg-blue-600 max-w-lg" : 
                        (role === "assistant" && (content.includes("PDF") && content.includes("uploaded successfully") || content.includes("Hi, I'm here to help"))) ? "bg-transparent max-w-4xl" : "bg-gray-700 max-w-lg"
                      }`}
                    >
                      {type === "reading_progress" && progress && (
                        <div className="mb-2">
                          <div className="w-full bg-gray-600 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${
                                  (progress.current / progress.total) * 100
                                }%`,
                              }}
                            ></div>
                          </div>
                          <p className="text-xs text-gray-300 mt-1">
                            {progress.current} of {total_lines} sections
                          </p>
                        </div>
                      )}
                      {type === "reading_pause" && (
                        <div className="text-yellow-300 text-sm mb-2">
                          ⏸️ Paused - Say &quot;continue&quot; or
                          &quot;stop&quot;
                        </div>
                      )}
                      {type === "reading_completed" && (
                        <div className="text-green-300 text-sm mb-2">
                          ✅ Reading completed!
                        </div>
                      )}
                      {type === "error" && (
                        <div className="text-red-300 text-sm mb-2">
                          ❌ Error
                        </div>
                      )}
                      
                      {/* Enhanced styling for specific messages */}
                      {role === "assistant" && (content.includes("PDF") && content.includes("uploaded successfully") || content.includes("Please start the conversation with the assistant by saying Hi Jessica")) ? (
                        <div className="w-full flex justify-center">
                          <div className="bg-green-900/30 border border-green-600/30 rounded-lg p-6 max-w-2xl text-center">
                            <div className="flex items-center justify-center mb-3">
                              <svg className="w-8 h-8 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                              <span className="text-green-300 font-bold text-xl">Document Uploaded Successfully!</span>
                            </div>
                            <p className="text-green-200 text-lg leading-relaxed">{content}</p>
                          </div>
                        </div>
                      ) : role === "assistant" && content.includes("Hi, I'm here to help") ? (
                        <div className="w-full flex justify-center">
                          <div className="bg-blue-900/30 border border-blue-600/30 rounded-lg p-6 max-w-2xl text-center">
                            <div className="flex items-center justify-center mb-3">
                              <svg className="w-8 h-8 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M12 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                              </svg>
                              <span className="text-blue-300 font-bold text-xl">Assistant Ready</span>
                            </div>
                            <p className="text-blue-200 text-lg leading-relaxed">{content}</p>
                          </div>
                        </div>
                      ) : (
                        <div className="text-lg">{content}</div>
                      )}

                      {/* Microphone Error Retry Button */}
                      {microphoneError && (
                        <div className="mt-4 flex gap-2">
                          <button
                            onClick={retryMicrophoneAccess}
                            className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-semibold transition-colors"
                          >
                            🎤 Retry Microphone Access
                          </button>
                          <button
                            onClick={openMicSettings}
                            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold transition-colors"
                          >
                            Open Settings
                          </button>
                        </div>
                      )}

                      {timestamp && (
                        <div className="text-xs text-gray-400 mt-1">
                          {new Date(timestamp).toLocaleTimeString()}
                        </div>
                      )}
                    </div>
                  </div>
                );
              }
            )}
            {currentTranscript && (
              <div className="w-full flex justify-end">
                <div className="p-4 rounded-lg max-w-lg bg-blue-600">
                  {currentTranscript}
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
            </div>

          </div>
        )}
        {pdfViewerVisible && currentSourceLocation && (
          <div className="flex justify-center mt-4">
            <button
              onClick={fetchHighlightedPDF}
              className="px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-semibold rounded shadow"
            >
              View Highlighted PDF (Backend)
            </button>
          </div>
        )}
      </main>

      {/* Conversation History Modal */}
      {showHistoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-gray-700">
              <h3 className="text-xl font-semibold text-white">
                Conversation History
              </h3>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowHistoryModal(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    clearConversationHistory();
                    setConversationHistory([]);
                  }}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Clear All
                </button>
              </div>
            </div>
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {conversationHistory.length === 0 ? (
                <p className="text-gray-400 text-center">
                  No conversation history found.
                </p>
              ) : (
                <div className="space-y-4">
                  {conversationHistory.map((conv, index) => (
                    <div
                      key={conv.conversationId}
                      className="border border-gray-700 rounded-lg p-4 hover:bg-gray-700/50 transition-colors"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="text-white font-medium">
                            Session {conv.conversationId.slice(-8)}
                          </h4>
                          <p className="text-gray-400 text-sm">
                            {new Date(conv.sessionStartTime).toLocaleString()}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={() =>
                              loadConversation(conv.conversationId)
                            }
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                          >
                            Load
                          </button>
                          <button
                            onClick={() => {
                              const updatedHistory = conversationHistory.filter(
                                (c) => c.conversationId !== conv.conversationId
                              );
                              setConversationHistory(updatedHistory);
                              // Remove from localStorage
                              const existingConversations = JSON.parse(
                                localStorage.getItem(
                                  CONVERSATION_STORAGE_KEY
                                ) || "{}"
                              );
                              delete existingConversations[conv.conversationId];
                              localStorage.setItem(
                                CONVERSATION_STORAGE_KEY,
                                JSON.stringify(existingConversations)
                              );
                            }}
                            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                      <div className="text-gray-300 text-sm">
                        <p>
                          <strong>Messages:</strong> {conv.messages.length}
                        </p>
                        <p>
                          <strong>Questions:</strong>{" "}
                          {
                            conv.messages.filter((m) => m.role === "user")
                              .length
                          }
                        </p>
                        {conv.currentDocument && (
                          <p>
                            <strong>Document:</strong>{" "}
                            {conv.currentDocument.name}
                          </p>
                        )}
                      </div>
                      {conv.messages.length > 0 && (
                        <div className="mt-3 text-gray-400 text-xs">
                          <p>
                            <strong>Last message:</strong>{" "}
                            {conv.messages[
                              conv.messages.length - 1
                            ].content.substring(0, 100)}
                            ...
                          </p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="p-6 border-t border-gray-700">
              <button
                onClick={startNewConversation}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start New Conversation
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Document Replacement Dialog */}
      {showReplaceDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                <svg
                  className="w-8 h-8 text-yellow-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Replace Document?
              </h3>
              <p className="text-gray-300 mb-6">
                You already have a document loaded:{" "}
                <strong className="text-blue-300">
                  {currentDocument?.name}
                </strong>
              </p>
              <p className="text-gray-400 text-sm mb-6">
                Uploading a new document will replace the current one and clear
                the conversation history.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={handleCancelReplace}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReplaceDocument}
                  className="flex-1 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
                >
                  Replace
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* PDF Upload Confirmation Dialog */}
      {showUploadConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                <svg
                  className="w-8 h-8 text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Confirm PDF Upload
              </h3>
              <p className="text-gray-300 mb-6">
                Is this the PDF you want to upload?
              </p>
              <div className="bg-gray-700 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-blue-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <div className="text-left">
                    <p className="text-white font-medium">
                      {pendingConfirmFile?.name}
                    </p>
                    <p className="text-gray-400 text-sm">
                      {(pendingConfirmFile?.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleCancelUpload}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  No, Choose Different File
                </button>
                <button
                  onClick={handleConfirmUpload}
                  className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Yes, Upload This PDF
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Welcome Confirmation Dialog */}
      {showWelcomeConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                <svg
                  className="w-8 h-8 text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Clear Everything?
              </h3>
              <p className="text-gray-300 mb-6">
                Going back to the welcome page will clear:
              </p>
              <ul className="text-gray-400 text-sm mb-6 text-left space-y-2">
                <li>• Current conversation and document</li>
                <li>• All conversation history</li>
                <li>• Any active voice sessions</li>
              </ul>
              <p className="text-gray-400 text-sm mb-6">
                This action cannot be undone.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowWelcomeConfirmModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    clearEverything();
                    setShowWelcomeConfirmModal(false);

                    // Handle navigation for static export
                    if (typeof window !== 'undefined') {
                      const isStaticExport = window.location.protocol === 'file:' || window.location.protocol === 'app:';

                      if (isStaticExport) {
                        window.location.href = '../index.html';
                      } else {
                        router.push("/welcome");
                      }
                    }
                  }}
                  className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Clear & Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* PDF Viewer Modal */}
      <PDFViewer
        documentUrl={
          currentDocument
            ? `${BACKEND_URL}/pdf/${
                currentDocument.documentId
              }/${encodeURIComponent(currentDocument.name)}`
            : null
        }
        sourceLocation={currentSourceLocation}
        isVisible={pdfViewerVisible}
        documentKey={currentDocument?.name || "no-document"}
        onClose={() => {
          setPdfViewerVisible(false);
          setCurrentSourceLocation(null);
        }}
      />

      {/* Highlighted PDF Modal - now opens automatically */}
      {showHighlightedPdf && highlightedPdfUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 flex flex-col">
            <div className="flex justify-between items-center p-4 border-b">
              <span className="font-semibold text-lg text-gray-800">
                Highlighted PDF
              </span>
              <button
                onClick={() => setShowHighlightedPdf(false)}
                className="text-gray-500 hover:text-gray-700 text-xl font-bold"
              >
                ✕
              </button>
            </div>
            <iframe
              src={
                highlightedPdfUrl +
                (currentSourceLocation?.page
                  ? `#page=${currentSourceLocation.page}`
                  : "")
              }
              width="100%"
              height="600px"
              title="Highlighted PDF"
            />
          </div>
        </div>
      )}

      {/* Microphone Permission Modal */}
      {showMicModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
            <div className="text-center">
              <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto ${micStatus === 'granted' ? 'bg-green-500/20' : 'bg-yellow-500/20'}`}>
                <svg className={`w-8 h-8 ${micStatus === 'granted' ? 'text-green-400' : 'text-yellow-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {micStatus === 'granted' ? 'Manage Microphone' : 'Microphone Access Required'}
              </h3>
              <p className="text-gray-300 mb-4">
                {micStatus === 'granted'
                  ? 'To revoke microphone access, please open system settings.'
                  : 'To allow microphone access, please open system settings and grant permission.'}
              </p>

              {/* Current Status */}
              <div className="bg-gray-700 rounded-lg p-3 mb-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-200 text-sm">Current Status:</span>
                  <div className="flex items-center gap-2">
                    <span className={`text-sm font-medium ${micStatus === 'granted' ? 'text-green-400' : micStatus === 'denied' ? 'text-red-400' : 'text-yellow-400'}`}>
                      {micStatus === 'granted' ? '✓ Granted' : micStatus === 'denied' ? '✗ Denied' : '⚠ Unknown'}
                    </span>
                    <button
                      onClick={handleRefreshPermission}
                      disabled={isCheckingPermission}
                      className="p-1 rounded hover:bg-gray-600 transition-colors disabled:opacity-50"
                      title="Refresh status"
                    >
                      <svg
                        className={`w-4 h-4 text-gray-400 hover:text-white ${isCheckingPermission ? 'animate-spin' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 mb-6">
                <p className="text-blue-200 text-sm">
                  💡 <strong>Tip:</strong> After changing permissions in System Settings, click the refresh button above or use the refresh button in the header to update the status.
                </p>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowMicModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  Close
                </button>
                <button
                  onClick={openMicSettings}
                  className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
                >
                  Open Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Test Audio Button removed */}

      <footer className="p-4 border-t border-gray-700 bg-gray-900">
        <div className={`wave mx-auto mb-4 ${isRunning ? "running" : ""}`} />
        <div className="text-center mb-6">
          {isReadingDocument ? (
            <p className="text-blue-300 font-medium text-base">
              Document reading mode - Say &quot;continue&quot; or &quot;stop&quot;
            </p>
          ) : isRunning ? (
            <p className="text-green-300 font-medium text-base">
            </p>
          ) : (
            <p className="text-gray-300 font-medium text-base">
              
            </p>
          )}
        </div>
        <div className="flex justify-center items-center gap-4 mb-6">
          {isRunning ? (
            <button
              className="w-56 bg-red-500 text-white px-4 py-3 rounded-full cursor-pointer hover:bg-red-600 transition-all duration-300 shadow-lg font-bold tracking-wide text-lg hover:shadow-xl transform hover:scale-105"
              onClick={endConversation}
            >
              End {isReadingDocument ? "Reading" : "Conversation"}
            </button>
          ) : null}
        </div>
        
        {/* Upload New PDF button - only show when conversation has ended and user has a document */}
        {!isRunning && currentDocument && (
          <div className="flex justify-center mb-4">
            <label className="cursor-pointer">
              <div className="w-56 px-4 py-3 rounded-full transition-all duration-300 shadow-lg font-bold tracking-wide text-lg bg-green-500 text-white hover:bg-green-600 hover:shadow-xl transform hover:scale-105 flex items-center justify-center gap-3">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Upload New PDF
                <input
                  type="file"
                  accept=".pdf"
                  onChange={handlePdfUpload}
                  className="hidden"
                />
              </div>
            </label>
          </div>
        )}
      </footer>
    </div>
  );
}

export default VoiceAssistant;
