'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import PDFViewer from '../PDFViewer';

function PDFViewerContent() {
  const searchParams = useSearchParams();
  const [pdfData, setPdfData] = useState(null);

  useEffect(() => {
    // Get PDF data from URL params
    const dataParam = searchParams.get('data');
    if (dataParam) {
      try {
        const data = JSON.parse(decodeURIComponent(dataParam));
        setPdfData(data);
      } catch (error) {
        console.error('Error parsing PDF data:', error);
      }
    }
  }, [searchParams]);

  const handleClose = () => {
    // Close the current window
    if (typeof window !== 'undefined' && window.close) {
      window.close();
    }
  };

  if (!pdfData) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900 text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading PDF viewer...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-900">
      <PDFViewer
        documentUrl={pdfData.documentUrl}
        sourceLocation={pdfData.sourceLocation}
        documentKey={pdfData.documentKey}
        isVisible={true}
        onClose={handleClose}
      />
    </div>
  );
}

export default function PDFViewerPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-screen bg-gray-900 text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading PDF viewer...</p>
        </div>
      </div>
    }>
      <PDFViewerContent />
    </Suspense>
  );
}