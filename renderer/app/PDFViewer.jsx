'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

// Dynamically import PDF.js only on the client side
let pdfjsLib = null;

const loadPDFJS = async () => {
  if (typeof window === 'undefined') {
    throw new Error('PDF.js can only be loaded on the client side');
  }

  if (!pdfjsLib) {
    // Load standard build and normalize namespace for ESM/CJS interop
    const mod = await import('pdfjs-dist/build/pdf');
    const pdfjs = mod && mod.default ? mod.default : mod;

    // Configure worker path for both development and production
    if (typeof window !== 'undefined') {
      const isElectron = window.location.protocol === 'app:';
      const isStaticFile = window.location.protocol === 'file:';
      const isDevelopment = window.location.hostname === 'localhost' && window.location.protocol === 'http:';

      if (isElectron) {
        // For Electron app:// protocol, use the worker from the app root
        pdfjs.GlobalWorkerOptions.workerSrc = 'app://localhost/pdf.worker.js';
      } else if (isStaticFile) {
        // For static file:// export, use relative path
        pdfjs.GlobalWorkerOptions.workerSrc = './pdf.worker.js';
      } else {
        // For development, use absolute path
        pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.js';
      }
    }

    pdfjsLib = pdfjs;
  }
  return pdfjsLib;
};

const PDFViewer = ({ documentUrl, sourceLocation, isVisible, onClose, documentKey }) => {
  const [pdfDocument, setPdfDocument] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [scale, setScale] = useState(1.0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const canvasRef = useRef(null);
  const [highlightedText, setHighlightedText] = useState(null);
  const [isRendering, setIsRendering] = useState(false);
  const [highlightRects, setHighlightRects] = useState([]);
  const [allTextRects, setAllTextRects] = useState([]);
  const [highlightPage, setHighlightPage] = useState(null);
  const [matchedText, setMatchedText] = useState(null);
  const [matchQuality, setMatchQuality] = useState(null);
  const renderTaskRef = useRef(null);
  const isUnmountedRef = useRef(false);

  useEffect(() => {
    isUnmountedRef.current = false;
    return () => {
      isUnmountedRef.current = true;
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
        renderTaskRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (isVisible && documentUrl && typeof window !== 'undefined') {
      // Clear old PDF state when switching documents
      setPdfDocument(null);
      // Don't reset currentPage here - let loadPDF handle it based on sourceLocation
      setHighlightRects([]);
      setAllTextRects([]);
      setHighlightedText(null);
      setHighlightPage(null);
      loadPDF();
    }
  }, [isVisible, documentUrl, documentKey, sourceLocation?.page]); // Added sourceLocation.page dependency

  useEffect(() => {
    if (sourceLocation && pdfDocument) {
      // Use the 'source' (original source chunk) for highlighting instead of the LLM's answer
      const sourceTextToHighlight = sourceLocation && sourceLocation.source ? sourceLocation.source : (sourceLocation.text || '');
      if (sourceTextToHighlight) {
        setTimeout(() => {
          // First, try to search on the specific page from sourceLocation
          const targetPage = sourceLocation.page || 1;
          console.log(`Searching for text on target page ${targetPage}`);
          
          searchTextOnSpecificPage(sourceTextToHighlight, targetPage).then(result => {
            if (result && result.page && result.rect) {
              setHighlightPage(result.page);
              
              // If we have all matching items, highlight all of them
              if (result.allMatchingItems && result.allMatchingItems.length > 0) {
                const resultScale = result.scale || scale; // Use stored scale or current scale
                
                // Get viewport from the current page
                const getViewportHeight = async () => {
                  try {
                    const page = await pdfDocument.getPage(result.page);
                    const viewport = page.getViewport({ scale: resultScale });
                    return viewport.height;
                  } catch (error) {
                    console.error('Error getting viewport height:', error);
                    return 800; // fallback height
                  }
                };
                
                getViewportHeight().then(viewportHeight => {
                  console.log(`Creating highlight rects for ${result.allMatchingItems.length} items:`);
                  result.allMatchingItems.forEach((item, idx) => {
                    console.log(`  Item ${idx}: "${item.str}"`);
                  });
                  
                  const allRects = result.allMatchingItems.map(item => ({
                    x: item.transform[4] * resultScale,
                    y: (viewportHeight - item.transform[5] * resultScale) - (item.height * resultScale),
                    width: item.width * resultScale,
                    height: item.height * resultScale,
                    text: item.str,
                    itemText: item.str.toLowerCase()
                  }));
                  
                  console.log(`Created ${allRects.length} highlight rects`);
                  setHighlightRects(allRects);
                  
                  // Set other state after highlighting is done - DON'T change the page!
                  setMatchedText(result.matchedText);
                  setMatchQuality(result.matchQuality);
                });
              } else if (result.rect) {
                // Fallback to single rect
                console.log('No allMatchingItems, using single rect');
                setHighlightRects([result.rect]);
                // DON'T change the page!
                setMatchedText(result.matchedText);
                setMatchQuality(result.matchQuality);
              } else {
                console.log('No result rect found');
                setHighlightRects([]);
                setMatchedText(null);
                setMatchQuality(null);
              }
            } else {
              setHighlightRects([]);
              setHighlightPage(null);
              setMatchedText(null);
              setMatchQuality(null);
            }
          });
        }, 200);
      }
    }
  }, [sourceLocation, pdfDocument]);

  // Add a separate useEffect to handle document URL changes
  useEffect(() => {
    if (documentUrl && isVisible) {
      console.log('Document URL changed:', documentUrl);
      // Clear all state when document URL changes
      setPdfDocument(null);
      // Don't reset currentPage here - let loadPDF handle it based on sourceLocation
      setHighlightRects([]);
      setAllTextRects([]);
      setHighlightedText(null);
      setLoading(true);
      setError(null);
    }
  }, [documentUrl]);

  // Reset highlight state when documentUrl or documentKey changes
  useEffect(() => {
    setHighlightRects([]);
    setHighlightPage(null);
    setHighlightedText(null);
    setMatchedText(null);
    setMatchQuality(null);
  }, [documentUrl, documentKey]);

  // Jump to the correct page when the viewer is opened and sourceLocation.page is set
  useEffect(() => {
    if (isVisible && sourceLocation?.page) {
      setCurrentPage(sourceLocation.page);
    }
  }, [isVisible, sourceLocation?.page]);

  // Single consolidated useEffect for rendering pages
  useEffect(() => {
    if (pdfDocument && currentPage > 0 && isVisible && !isRendering) {
      console.log(`useEffect: Rendering page ${currentPage}`);
      renderPage(currentPage);
    }
  }, [currentPage, pdfDocument, scale, isVisible]);

  const loadPDF = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('Loading PDF.js library...');
      const pdfjs = await loadPDFJS();
      console.log('PDF.js loaded successfully, worker configured');

      const pdfUrl = documentUrl || '/api/pdf-placeholder';
      console.log('Loading PDF from URL:', pdfUrl);

      const pdf = await pdfjs.getDocument(pdfUrl).promise;
      
      setPdfDocument(pdf);
      setTotalPages(pdf.numPages);
      
      // Set the initial page based on sourceLocation if available
      const initialPage = sourceLocation?.page || 1;
      setCurrentPage(initialPage);
      console.log(`PDF loaded, setting initial page to: ${initialPage}`);
    } catch (error) {
      console.error('Error loading PDF:', error);
      setError(`Failed to load PDF: ${error.message}. Please try again.`);
    } finally {
      setLoading(false);
    }
  }, [documentUrl, sourceLocation?.page]);

  const renderPage = useCallback(async (pageNum) => {
    if (!pdfDocument) return;
    
    // Prevent multiple simultaneous renders
    if (isRendering) {
      console.log('Already rendering, skipping render request for page', pageNum);
      return;
    }

    // Cancel any ongoing render task and wait for it to finish
    if (renderTaskRef.current) {
      try {
        renderTaskRef.current.cancel();
        await renderTaskRef.current.promise.catch(() => {});
      } catch (e) {
        // Ignore if already cancelled
      }
      renderTaskRef.current = null;
    }

    setIsRendering(true);
    try {
      const page = await pdfDocument.getPage(pageNum);
      const canvas = canvasRef.current;
      if (!canvas) {
        console.error('Canvas not found');
        return;
      }
      const context = canvas.getContext('2d');
      const viewport = page.getViewport({ scale });
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      const renderContext = {
        canvasContext: context,
        viewport: viewport
      };
      // Start new render task and store it
      const renderTask = page.render(renderContext);
      renderTaskRef.current = renderTask;
      await renderTask.promise;
      renderTaskRef.current = null;

      // Debug: Log all text on this page
      const textContent = await page.getTextContent();
      console.log(`Page ${pageNum} text items:`, textContent.items.map(item => item.str));
      
      // Only highlight if we have source text AND we're on the page where the text was found
      if (sourceLocation && sourceLocation.text && currentPage === pageNum) {
        await highlightText(sourceLocation, viewport);
      } else {
        setHighlightRects([]);
        setAllTextRects([]);
        setHighlightedText(null);
      }
    } catch (error) {
      if (error?.name === 'RenderingCancelledException') {
        // Ignore cancelled renders
      } else {
        console.error(`Error rendering page ${pageNum}:`, error);
      }
    } finally {
      if (!isUnmountedRef.current) setIsRendering(false);
    }
  }, [pdfDocument, scale, sourceLocation, currentPage, isRendering]);

  const highlightText = async (location, viewport) => {
    if (!pdfDocument || !location || !viewport) return;
    try {
      const page = await pdfDocument.getPage(currentPage);
      const textContent = await page.getTextContent();
      const textItems = textContent.items;
      const sourceText = location.text?.replace(/\s+/g, ' ').trim().toLowerCase();
      let rects = [];
      let allRects = [];
      
      console.log(`\n=== HIGHLIGHTING ON PAGE ${currentPage} ===`);
      console.log('Source text to highlight:', sourceText);
      
      // Collect all text items for debugging
      textItems.forEach((item, index) => {
        const itemText = item.str.replace(/\s+/g, ' ').trim().toLowerCase();
        const x = item.transform[4] * scale;
        const y = (viewport.height - item.transform[5] * scale) - (item.height * scale);
        allRects.push({
          x,
          y,
          width: item.width * scale,
          height: item.height * scale,
          text: item.str,
          itemText: itemText
        });
      });
      
      if (sourceText && sourceText.length > 0) {
        // Split source text into words
        const sourceWords = sourceText.split(/\s+/).filter(w => w.length > 0);
        console.log(`Source words: [${sourceWords.join(', ')}]`);
        
        // Create sliding window approach with 10-word windows
        const windowSize = 10;
        let bestMatches = [];
        let bestQuality = 0;
        
        // Try different window sizes if 10 doesn't work well
        for (let currentWindowSize = Math.min(windowSize, textItems.length); currentWindowSize >= 3; currentWindowSize--) {
          console.log(`Trying window size: ${currentWindowSize}`);
          
          for (let i = 0; i <= textItems.length - currentWindowSize; i++) {
            const windowItems = textItems.slice(i, i + currentWindowSize);
            const windowText = windowItems.map(item => item.str).join(' ');
            const normalizedWindowText = windowText.replace(/\s+/g, ' ').trim().toLowerCase();
            
            console.log(`Window ${i}: "${normalizedWindowText}"`);
            
            // Count matching words
            let matchingWords = 0;
            let matchedSourceWords = new Set();
            
            for (const sourceWord of sourceWords) {
              if (normalizedWindowText.includes(sourceWord)) {
                matchingWords++;
                matchedSourceWords.add(sourceWord);
              }
            }
            
            const matchQuality = matchingWords / sourceWords.length;
            
            console.log(`  Match quality: ${matchQuality.toFixed(2)} (${matchingWords}/${sourceWords.length} words)`);
            console.log(`  Matched words: [${Array.from(matchedSourceWords).join(', ')}]`);
            
            if (matchQuality > bestQuality) {
              bestQuality = matchQuality;
              bestMatches = windowItems.map(item => ({
                x: item.transform[4] * scale,
                y: (viewport.height - item.transform[5] * scale) - (item.height * scale),
                width: item.width * scale,
                height: item.height * scale,
                text: item.str,
                itemText: item.str.toLowerCase()
              }));
              
              console.log(`  *** NEW BEST MATCH: Quality ${matchQuality.toFixed(2)} ***`);
            }
          }
          
          // If we found a good match with this window size, stop trying smaller windows
          if (bestQuality > 0.5) {
            console.log(`Good match found with window size ${currentWindowSize}, stopping search`);
            break;
          }
        }
        
        // Highlight if we have a reasonable match
        if (bestMatches.length > 0 && bestQuality > 0.2) {
          rects.push(...bestMatches);
          console.log(`*** FINAL HIGHLIGHT: "${bestMatches.map(r => r.text).join(' ')}" (Quality: ${bestQuality.toFixed(2)}) ***`);
          
          if (bestQuality < 0.6) {
            console.log('Warning: Low quality match - highlight may not be accurate');
          }
        } else {
          console.log('No good match found - skipping highlight');
        }
      }
      
      setHighlightRects(rects);
      setAllTextRects(allRects);
      if (rects.length === 0) setHighlightedText(null);
      console.log('Final highlight rects:', rects.map(r => r.text));
    } catch (error) {
      console.error('Error highlighting text:', error);
    }
  };

  const changePage = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const changeScale = (newScale) => {
    setScale(Math.max(0.5, Math.min(3.0, newScale)));
  };

  const searchTextOnSpecificPage = async (searchText, targetPage) => {
    if (!pdfDocument || !searchText || !targetPage) return null;
    
    // Use the original source text without aggressive truncation
    let normalizedSearchText = searchText.replace(/\s+/g, ' ').trim().toLowerCase();
    
    console.log(`=== SEARCHING FOR: "${normalizedSearchText}" ON PAGE ${targetPage} ===`);
    console.log(`Original source text length: ${searchText.length} characters`);
    
    try {
      const page = await pdfDocument.getPage(targetPage);
      const textContent = await page.getTextContent();
      const textItems = textContent.items;
      
      // Create a continuous text stream from all text items on this page
      const pageText = textItems.map(item => item.str).join(' ');
      const normalizedPageText = pageText.replace(/\s+/g, ' ').trim().toLowerCase();
      
      console.log(`Page ${targetPage} text: "${normalizedPageText.substring(0, 200)}..."`);
      
      // Strategy 1: Look for exact substring match in the continuous text
      if (normalizedPageText.includes(normalizedSearchText)) {
        console.log(`*** EXACT SUBSTRING MATCH FOUND on page ${targetPage} ***`);
        console.log(`Searching for: "${normalizedSearchText}"`);
        console.log(`Found in page text at position: ${normalizedPageText.indexOf(normalizedSearchText)}`);
        
        // Find the position of the match in the continuous text
        const matchStart = normalizedPageText.indexOf(normalizedSearchText);
        const matchEnd = matchStart + normalizedSearchText.length;
        
        console.log(`Match spans from position ${matchStart} to ${matchEnd}`);
        console.log(`Matched text: "${normalizedPageText.substring(matchStart, matchEnd)}"`);
        
        // Find which text items contain this match
        let currentPos = 0;
        let matchItems = [];
        
        console.log('Analyzing text items:');
        for (let i = 0; i < textItems.length; i++) {
          const item = textItems[i];
          const itemText = item.str;
          const itemStart = currentPos;
          const itemEnd = currentPos + itemText.length;
          
          console.log(`Item ${i}: "${itemText}" (positions ${itemStart}-${itemEnd})`);
          
          // Check if this item overlaps with our match
          if (itemEnd > matchStart && itemStart < matchEnd) {
            matchItems.push(item);
            console.log(`  -> INCLUDED in match`);
          }
          
          currentPos += itemText.length + 1; // +1 for space
        }
        
        console.log(`Total matching items: ${matchItems.length}`);
        matchItems.forEach((item, idx) => {
          console.log(`  Match item ${idx}: "${item.str}"`);
        });
        
        if (matchItems.length > 0) {
          // Use the first matching item for positioning, but store all matching items
          const firstItem = matchItems[0];
          const rect = {
            x: firstItem.transform[4] * scale,
            y: (page.getViewport({ scale }).height - firstItem.transform[5] * scale) - (firstItem.height * scale),
            width: firstItem.width * scale,
            height: firstItem.height * scale,
            text: firstItem.str,
            itemText: firstItem.str.toLowerCase(),
            allMatchingItems: matchItems // Store all matching items for precise highlighting
          };
          
          return {
            page: targetPage,
            rect: rect,
            count: normalizedSearchText.split(/\s+/).length,
            matchQuality: 1.0,
            matchedText: normalizedSearchText,
            allMatchingItems: matchItems,
            scale: scale,
            viewportHeight: page.getViewport({ scale }).height
          };
        }
      }
      
      // Strategy 2: If no exact match, look for best partial match using sliding window
      const searchWords = normalizedSearchText.split(/\s+/).filter(w => w.length > 0);
      
      // Try different window sizes
      for (let windowSize = 3; windowSize <= 15; windowSize++) {
        for (let i = 0; i <= textItems.length - windowSize; i++) {
          const windowItems = textItems.slice(i, i + windowSize);
          const windowText = windowItems.map(item => item.str).join(' ');
          const normalizedWindowText = windowText.replace(/\s+/g, ' ').trim().toLowerCase();
          
          // Count matching words
          const windowWords = normalizedWindowText.split(/\s+/);
          const matchingWords = searchWords.filter(searchWord => 
            windowWords.some(windowWord => 
              windowWord.includes(searchWord) || searchWord.includes(windowWord)
            )
          );
          
          const matchQuality = matchingWords.length / searchWords.length;
          
          if (matchQuality > 0.5) { // Lower threshold for single page search
            const firstItem = windowItems[0];
            
            // Only include the actual matched words in the matchedText
            const matchedTextItems = windowItems.filter(item => {
              const itemText = item.str.toLowerCase();
              return matchingWords.some(word => itemText.includes(word));
            });
            const actualMatchedText = matchedTextItems.map(item => item.str).join(' ');
            
            return {
              page: targetPage,
              rect: {
                x: firstItem.transform[4] * scale,
                y: (page.getViewport({ scale }).height - firstItem.transform[5] * scale) - (firstItem.height * scale),
                width: firstItem.width * scale,
                height: firstItem.height * scale,
                text: firstItem.str,
                itemText: normalizedWindowText
              },
              count: matchingWords.length,
              matchQuality: matchQuality,
              matchedText: actualMatchedText,
              allMatchingItems: matchedTextItems
            };
          }
        }
      }
      
      console.log(`No match found on page ${targetPage}`);
      return null;
      
    } catch (error) {
      console.error(`Error searching page ${targetPage}:`, error);
      return null;
    }
  };

  const searchTextAcrossPages = async (searchText) => {
    if (!pdfDocument || !searchText) return null;
    
    // Use the original source text without aggressive truncation
    let normalizedSearchText = searchText.replace(/\s+/g, ' ').trim().toLowerCase();
    
    console.log(`=== SEARCHING FOR: "${normalizedSearchText}" ===`);
    console.log(`Original source text length: ${searchText.length} characters`);
    
    let bestOverall = { page: null, rect: null, count: 0, matchQuality: 0 };
    
    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      try {
        const page = await pdfDocument.getPage(pageNum);
        const textContent = await page.getTextContent();
        const textItems = textContent.items;
        
        // Create a continuous text stream from all text items on this page
        const pageText = textItems.map(item => item.str).join(' ');
        const normalizedPageText = pageText.replace(/\s+/g, ' ').trim().toLowerCase();
        
        console.log(`Page ${pageNum} text: "${normalizedPageText.substring(0, 200)}..."`);
        
        // Strategy 1: Look for exact substring match in the continuous text
        if (normalizedPageText.includes(normalizedSearchText)) {
          console.log(`*** EXACT SUBSTRING MATCH FOUND on page ${pageNum} ***`);
          console.log(`Searching for: "${normalizedSearchText}"`);
          console.log(`Found in page text at position: ${normalizedPageText.indexOf(normalizedSearchText)}`);
          
          // Find the position of the match in the continuous text
          const matchStart = normalizedPageText.indexOf(normalizedSearchText);
          const matchEnd = matchStart + normalizedSearchText.length;
          
          console.log(`Match spans from position ${matchStart} to ${matchEnd}`);
          console.log(`Matched text: "${normalizedPageText.substring(matchStart, matchEnd)}"`);
          
          // Find which text items contain this match
          let currentPos = 0;
          let matchItems = [];
          
          console.log('Analyzing text items:');
          for (let i = 0; i < textItems.length; i++) {
            const item = textItems[i];
            const itemText = item.str;
            const itemStart = currentPos;
            const itemEnd = currentPos + itemText.length;
            
            console.log(`Item ${i}: "${itemText}" (positions ${itemStart}-${itemEnd})`);
            
            // Check if this item overlaps with our match
            if (itemEnd > matchStart && itemStart < matchEnd) {
              matchItems.push(item);
              console.log(`  -> INCLUDED in match`);
            }
            
            currentPos += itemText.length + 1; // +1 for space
          }
          
          console.log(`Total matching items: ${matchItems.length}`);
          matchItems.forEach((item, idx) => {
            console.log(`  Match item ${idx}: "${item.str}"`);
          });
          
          if (matchItems.length > 0) {
            // Use the first matching item for positioning, but store all matching items
            const firstItem = matchItems[0];
            const rect = {
              x: firstItem.transform[4] * scale,
              y: (page.getViewport({ scale }).height - firstItem.transform[5] * scale) - (firstItem.height * scale),
              width: firstItem.width * scale,
              height: firstItem.height * scale,
              text: firstItem.str,
              itemText: firstItem.str.toLowerCase(),
              allMatchingItems: matchItems // Store all matching items for precise highlighting
            };
            
            bestOverall = {
              page: pageNum,
              rect: rect,
              count: normalizedSearchText.split(/\s+/).length,
              matchQuality: 1.0,
              matchedText: normalizedSearchText,
              allMatchingItems: matchItems,
              scale: scale,
              viewportHeight: page.getViewport({ scale }).height
            };
            
            console.log(`*** EXACT MATCH: "${normalizedSearchText}" (${matchItems.length} items) ***`);
            break; // Found exact match, no need to search further
          }
        }
        
        // Strategy 2: If no exact match, look for best partial match using sliding window
        if (bestOverall.matchQuality < 0.8) {
          const searchWords = normalizedSearchText.split(/\s+/).filter(w => w.length > 0);
          
          // Try different window sizes
          for (let windowSize = 3; windowSize <= 15; windowSize++) {
            for (let i = 0; i <= textItems.length - windowSize; i++) {
              const windowItems = textItems.slice(i, i + windowSize);
              const windowText = windowItems.map(item => item.str).join(' ');
              const normalizedWindowText = windowText.replace(/\s+/g, ' ').trim().toLowerCase();
              
              // Count matching words
              const windowWords = normalizedWindowText.split(/\s+/);
              const matchingWords = searchWords.filter(searchWord => 
                windowWords.some(windowWord => 
                  windowWord.includes(searchWord) || searchWord.includes(windowWord)
                )
              );
              
              const matchQuality = matchingWords.length / searchWords.length;
              
              if (matchQuality > bestOverall.matchQuality) {
                const firstItem = windowItems[0];
                
                // Only include the actual matched words in the matchedText
                const matchedTextItems = windowItems.filter(item => {
                  const itemText = item.str.toLowerCase();
                  return matchingWords.some(word => itemText.includes(word));
                });
                const actualMatchedText = matchedTextItems.map(item => item.str).join(' ');
                
                bestOverall = {
                  page: pageNum,
                  rect: {
                    x: firstItem.transform[4] * scale,
                    y: (page.getViewport({ scale }).height - firstItem.transform[5] * scale) - (firstItem.height * scale),
                    width: firstItem.width * scale,
                    height: firstItem.height * scale,
                    text: firstItem.str,
                    itemText: normalizedWindowText
                  },
                  count: matchingWords.length,
                  matchQuality: matchQuality,
                  matchedText: actualMatchedText,
                  allMatchingItems: matchedTextItems
                };
                
                console.log(`*** PARTIAL MATCH: "${actualMatchedText}" (Quality: ${matchQuality.toFixed(2)}) ***`);
              }
            }
          }
        }
        
      } catch (error) {
        console.error(`Error searching page ${pageNum}:`, error);
      }
    }
    
    if (bestOverall.page && bestOverall.rect && bestOverall.count > 0) {
      console.log(`*** BEST MATCH: Page ${bestOverall.page}, Quality: ${bestOverall.matchQuality.toFixed(2)}, Text: "${bestOverall.matchedText}" ***`);
      return bestOverall;
    }
    
    console.log(`\n=== TEXT "${normalizedSearchText}" NOT FOUND ON ANY PAGE ===`);
    return null;
  };



  // Debug: Log when highlightRects change
  useEffect(() => {
    console.log(`DEBUG: highlightRects changed to:`, highlightRects);
    console.log(`DEBUG: currentPage: ${currentPage}, sourceLocation.page: ${sourceLocation?.page}`);
  }, [highlightRects, currentPage, sourceLocation]);

  if (!isVisible || typeof window === 'undefined') return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl max-h-[95vh] w-full mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-800">
              {sourceLocation?.document_name || 'PDF Viewer'}
            </h2>
            {sourceLocation && (
              <span className="text-sm text-gray-600 bg-blue-100 px-2 py-1 rounded">
                Page {currentPage} of {totalPages}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Page Navigation */}
            {pdfDocument && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => changePage(currentPage - 1)}
                  disabled={currentPage <= 1}
                  className="px-3 py-1 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded text-sm"
                >
                  ←
                </button>
                <span className="text-sm text-gray-600">
                  {currentPage} / {totalPages}
                </span>
                <button
                  onClick={() => changePage(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className="px-3 py-1 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded text-sm"
                >
                  →
                </button>
              </div>
            )}
            
            {/* Zoom Controls */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => changeScale(scale - 0.2)}
                className="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm"
              >
                -
              </button>
              <span className="text-sm text-gray-600 w-12 text-center">
                {Math.round(scale * 100)}%
              </span>
              <button
                onClick={() => changeScale(scale + 0.2)}
                className="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-sm"
              >
                +
              </button>
            </div>
            
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-xl font-bold"
            >
              ✕
            </button>
          </div>
        </div>
        
        {/* PDF Content */}
        <div className="flex-1 overflow-auto p-4 bg-gray-100">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600">Loading PDF...</span>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="text-red-500 text-xl mb-2">⚠️</div>
                <p className="text-gray-600 mb-4">{error}</p>
                <button
                  onClick={loadPDF}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : pdfDocument ? (
            <div className="flex justify-center">
              <div className="relative border border-gray-300 shadow-lg bg-white">
                <canvas
                  ref={canvasRef}
                />
                {/* Highlight overlay removed */}
              </div>
            </div>
          ) : (
            <div className="text-center">
              <p className="text-gray-600 mb-4">PDF Viewer Ready</p>
              <p className="text-gray-500">PDF content will be displayed here when loaded.</p>
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div>
              {sourceLocation && (
                <span>Showing source from <strong>{sourceLocation.document_name}</strong></span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <span>←/→: navigate</span>
              <span>+/-: zoom</span>
              <span>ESC: close</span>
            </div>
          </div>
          
          {/* Matched Text Information */}
          {matchedText && (
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-yellow-800">Highlighted Text:</span>
                {matchQuality && (
                  <span className={`text-xs px-2 py-1 rounded ${
                    matchQuality > 0.8 ? 'bg-green-100 text-green-800' :
                    matchQuality > 0.6 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    Match Quality: {(matchQuality * 100).toFixed(0)}%
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-700 italic">&quot;{matchedText}&quot;</p>
              {sourceLocation?.text && (
                <div className="mt-2">
                  <span className="text-xs text-gray-500">Original source: </span>
                  <span className="text-xs text-gray-600">&quot;{sourceLocation.text.substring(0, 100)}...&quot;</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PDFViewer; 