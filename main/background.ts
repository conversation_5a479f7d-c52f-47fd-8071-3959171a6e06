import path from 'path'
import { app, ipcMain, systemPreferences, shell } from 'electron'
import serve from 'electron-serve'
import { createWindow } from './helpers'

const isProd = process.env.NODE_ENV === 'production'

if (isProd) {
  serve({ directory: 'app', scheme: 'app', hostname: 'localhost' })
} else {
  app.setPath('userData', `${app.getPath('userData')} (development)`)
}

// Enable better file handling
if (isProd) {
  app.whenReady().then(() => {
    // Register custom file protocol for better asset loading
    const { protocol } = require('electron')
    protocol.registerFileProtocol('file', (request, callback) => {
      const pathname = decodeURI(request.url.replace('file:///', ''))
      callback(pathname)
    })
  })
}

;(async () => {
  await app.whenReady()

  const mainWindow = createWindow('main', {
    width: 1000,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false, // Temporarily disable for local files
      allowRunningInsecureContent: false,
      experimentalFeatures: true,
    },
  })

  if (isProd) {
    try {
      await mainWindow.loadURL('app://localhost/index.html')
    } catch (error) {
      console.error('Failed to load app:// URL, trying file:// fallback', error)
      // Fallback to file protocol
      const indexPath = path.join(__dirname, '../app/index.html')
      await mainWindow.loadFile(indexPath)
    }
  } else {
    const port = process.argv[2]
    await mainWindow.loadURL(`http://localhost:${port}/`)
    mainWindow.webContents.openDevTools()
  }
})()

app.on('window-all-closed', () => {
  app.quit()
})

ipcMain.on('message', async (event, arg) => {
  event.reply('message', `${arg} World!`)
})

// Handle PDF viewer window creation
ipcMain.handle('open-pdf-viewer', async (event, { documentUrl, sourceLocation, documentKey }) => {
  const pdfWindow = createWindow('pdf-viewer', {
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false,
      allowRunningInsecureContent: false,
    },
  })

  // Store PDF data for the window
  const pdfData = {
    documentUrl,
    sourceLocation,
    documentKey
  }

  if (isProd) {
    try {
      await pdfWindow.loadURL(`app://localhost/pdf-viewer/index.html?data=${encodeURIComponent(JSON.stringify(pdfData))}`)
    } catch (error) {
      console.error('Failed to load PDF viewer, trying file:// fallback', error)
      const indexPath = path.join(__dirname, '../app/pdf-viewer/index.html')
      await pdfWindow.loadFile(indexPath, {
        query: { data: JSON.stringify(pdfData) }
      })
    }
  } else {
    const port = process.argv[2]
    await pdfWindow.loadURL(`http://localhost:${port}/pdf-viewer?data=${encodeURIComponent(JSON.stringify(pdfData))}`)
  }

  return pdfWindow.id
})

// Microphone permission handlers
ipcMain.handle('check-mic-permission', async () => {
  if (process.platform === 'darwin') {
    let status = systemPreferences.getMediaAccessStatus('microphone')
    console.log('macOS mic permission status:', status)

    // If status is 'not-determined', request permission to get fresh state
    if (status === 'not-determined') {
      console.log('Status is not-determined, requesting permission...')
      const granted = await systemPreferences.askForMediaAccess('microphone')
      status = granted ? 'granted' : 'denied'
      console.log('After request, new status:', status)
    }

    return { status }
  }

  // Windows: Check actual permission status
  if (process.platform === 'win32') {
    const status = systemPreferences.getMediaAccessStatus('microphone')
    console.log('Windows mic permission status:', status)
    return { status }
  }

  // Linux/other platforms - assume granted (no API available)
  console.log('Other platform, assuming granted')
  return { status: 'granted' }
})

ipcMain.handle('request-mic-permission', async () => {
  if (process.platform === 'darwin') {
    const granted = await systemPreferences.askForMediaAccess('microphone')
    console.log('macOS permission request result:', granted)
    return { granted }
  }

  // Windows: Cannot programmatically request, check current status instead
  if (process.platform === 'win32') {
    const status = systemPreferences.getMediaAccessStatus('microphone')
    console.log('Windows permission check result:', status)
    return { granted: status === 'granted' }
  }

  // Linux/other platforms - assume granted
  return { granted: true }
})

ipcMain.handle('open-mic-settings', async () => {
  if (process.platform === 'darwin') {
    await shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone')
  } else if (process.platform === 'win32') {
    await shell.openExternal('ms-settings:privacy-microphone')
  }
})
