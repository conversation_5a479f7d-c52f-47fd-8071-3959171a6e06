@import 'tailwindcss';

@theme {
  --font-*: initial;
  --font-sans: var(--font-open-sans);
  --font-urbanist: var(--font-urbanist);

  --color-primary-blue: rgb(146, 179, 202);
  --color-primary-orange: rgb(243, 195, 177);
  --color-main-text: rgb(0, 43, 49);
  --color-light-gray: rgb(244, 244, 244);
}

@layer base {
  body, html, #root {
    @apply text-main-text h-full overflow-x-hidden bg-gray-900;
  }

  ::-webkit-scrollbar { /* unify color to dark so it doesn't appear white */
    @apply h-4 w-2 bg-gray-900;
  }

  ::-webkit-scrollbar:horizontal {
    @apply h-4 w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-900 rounded;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-700 border border-gray-700 rounded;
  }
}

@layer components {
  .user-bubble, .assistant-bubble {
    @apply inline-block max-w-[80%] sm:max-w-[450px] py-2 px-4 rounded-2xl;
  }

  .user-bubble {
    @apply bg-light-gray text-main-text;
  }

  .assistant-bubble {
    @apply self-end bg-primary-blue/85 text-white;
  }

  .wave {
    width: 130px;
    height: 40px;
    position: relative;
  }

  .wave::before,
  .wave::after {
    content: "";
    position: absolute;
    bottom: 0;
    width: 10px;
    height: 10px;
    background-color: #6366f1;
    border-radius: 50%;
    animation: wave 1.5s infinite ease-in-out;
  }

  .wave::before {
    left: 30px;
    animation-delay: 0.1s;
  }

  .wave::after {
    left: 60px;
    animation-delay: 0.2s;
  }

  .wave.running::before,
  .wave.running::after {
    animation-play-state: running;
  }

  /* Document reading progress bar */
  .reading-progress {
    @apply w-full bg-gray-600 rounded-full h-2 overflow-hidden;
  }

  .reading-progress-bar {
    @apply bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out;
  }

  /* Reading status indicators */
  .reading-status {
    @apply text-sm font-medium mb-2;
  }

  .reading-status.paused {
    @apply text-yellow-300;
  }

  .reading-status.completed {
    @apply text-green-300;
  }

  .reading-status.error {
    @apply text-red-300;
  }

  /* Spotify-like reading content */
  .reading-content-lyrics {
    width: 100%;
    text-align: center;
  }
  
  .lyrics-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    max-height: 60vh;
    overflow-y: auto;
    padding: 2rem 0;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .lyrics-container::-webkit-scrollbar {
    display: none;
  }
  
  /* Utility to hide scrollbars but keep scrolling functionality */
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide vertical scrollbar ONLY for the welcome page */
  .welcome-page {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .welcome-page::-webkit-scrollbar { /* Chrome, Safari */
    display: none;
  }
  
  .lyric-line {
    font-weight: 600;
    font-size: 1.5rem;
    line-height: 1.5;
    padding: 0.5rem;
    margin: 0.25rem 0;
    transition: all 0.3s ease-in-out;
  }
  
  .lyrics-container .lyric-line.current {
    color: #ffffff !important;
    transform: scale(1.05) !important;
    background-color: rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }
  
  .lyrics-container .lyric-line.completed {
    color: #9ca3af;
    opacity: 0.8;
    transform: scale(0.95);
  }
  
  .lyrics-container .lyric-line.upcoming {
    color: #4b5563;
    opacity: 0.7;
    transform: scale(0.95);
  }
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

@keyframes wave {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}