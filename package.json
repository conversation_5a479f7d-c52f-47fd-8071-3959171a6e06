{"private": true, "name": "ivr-voice-assistant", "description": "IVR Voice Assistant", "version": "1.0.0", "author": "PMCS Services", "main": "app/background.js", "scripts": {"dev": "nextron", "build": "nextron build", "build:win": "nextron build --win", "build:mac": "nextron build --mac", "build:linux": "nextron build --linux", "build:all": "nextron build --win --mac --linux", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"electron-serve": "^1.3.0", "electron-store": "^8.2.0", "pdfjs-dist": "5.3.31"}, "devDependencies": {"@types/node": "^20.11.16", "@types/react": "^18.2.52", "@tailwindcss/postcss": "^4.1.8", "autoprefixer": "^10.4.19", "electron": "^34.0.0", "electron-builder": "^24.13.3", "eslint": "^9", "eslint-config-next": "15.3.3", "next": "15.3.3", "nextron": "^9.5.0", "postcss": "^8.4.38", "react": "^19", "react-dom": "^19", "tailwindcss": "^4.1.8", "typescript": "^5.7.3"}}