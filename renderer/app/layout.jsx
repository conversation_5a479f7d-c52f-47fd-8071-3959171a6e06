import { Open_Sans, Urbanist } from 'next/font/google';
import './index.css';

export const metadata = {
  title: 'IVR Assistant by PMCS',
  description: 'AI Voice Assistant powered by PMCS Services.',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.png',
  },
};

const openSans = Open_Sans({
  subsets: ['latin'],
  variable: '--font-open-sans',
});

const urbanist = Urbanist({
  subsets: ['latin'],
  variable: '--font-urbanist',
});

export default function RootLayout({ children }) {
  return (
    <html lang='en' className={`${openSans.variable} ${urbanist.variable} overflow-x-hidden`}>
      <head>
        <meta
          httpEquiv="Content-Security-Policy"
          content="default-src 'self' https://ivr-backend-prod.azurewebsites.net; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss://ivr-backend-prod.azurewebsites.net https://ivr-backend-prod.azurewebsites.net; object-src 'self' https://ivr-backend-prod.azurewebsites.net; worker-src 'self' blob:; child-src 'self' blob:; frame-src 'self' https://ivr-backend-prod.azurewebsites.net;"
        />
      </head>
      <body className="overflow-x-hidden">{children}</body>
    </html>
  );
}
