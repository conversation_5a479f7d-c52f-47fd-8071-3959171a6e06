function conversationReducer(state, action) {
  switch(action.type) {
    // Reset the conversation state to its initial values
    case 'reset': {
      return { 
        messages: [], 
        finalTranscripts: [], 
        interimTranscript: '',
        conversationId: null,
        sessionStartTime: null
      };
    }
    
    // Initialize a new conversation session
    case 'start_conversation': {
      return {
        ...state,
        conversationId: action.conversationId || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        sessionStartTime: new Date().toISOString(),
        messages: [],
        finalTranscripts: [],
        interimTranscript: ''
      };
    }
    
    // Update the interim transcript with the latest content
    case 'transcript_interim': {
      return {
        ...state,
        interimTranscript: action.content
      };
    }
    
    // Append the final transcript to the list of final transcripts
    case 'transcript_final': {
      return {
        ...state,
        finalTranscripts: [...state.finalTranscripts, action.content]
      };
    }
    
    case 'user_message_final': {
        const userMessage = { 
          role: 'user', 
          content: action.content,
          timestamp: new Date().toISOString(),
          messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };
        return {
            ...state,
            messages: [...state.messages, userMessage],
            finalTranscripts: [],
            interimTranscript: ''
        };
    }
    
    // Add the assistant message to the list of messages
    case 'assistant': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    case 'assistant_response': {
      const lastMessage = state.messages[state.messages.length - 1];
      if (lastMessage && lastMessage.role === 'assistant') {
        const updatedMessages = [...state.messages];
        updatedMessages[updatedMessages.length - 1] = {
          ...lastMessage,
          content: lastMessage.content + action.content
        };
        return { ...state, messages: updatedMessages };
      }
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage]
      };
    }
    
    // Document reading specific cases
    case 'document_reader_started': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        type: 'document_reader_started',
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    case 'reading_progress': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        type: 'reading_progress',
        progress: { current: action.current, total: action.total },
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    case 'reading_pause': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        type: 'reading_pause',
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    case 'reading_stopped': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        type: 'reading_stopped',
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    case 'reading_completed': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        type: 'reading_completed',
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    case 'error': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: action.content,
        type: 'error',
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    // Spotify-like reading cases
    case 'reading_lines_ready': {
      const newAssistantMessage = { 
        role: 'assistant', 
        content: ``, // Content will be the lyrics display
        type: 'reading_lyrics',
        lines: action.lines,
        section: action.section,
        total_lines: action.total_lines,
        current_line_index: -1, // Start at -1, first update will be 0
        timestamp: new Date().toISOString(),
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
      return {
        ...state,
        messages: [...state.messages, newAssistantMessage],
      };
    }
    
    case 'reading_highlight_line': {
      const lastMessageIndex = state.messages.length - 1;
      if (lastMessageIndex >= 0) {
        const lastMessage = state.messages[lastMessageIndex];
        if (lastMessage.role === 'assistant' && lastMessage.type === 'reading_lyrics') {
          const updatedMessages = [...state.messages];
          updatedMessages[lastMessageIndex] = {
            ...lastMessage,
            current_line_index: action.current_line_index,
          };
          return { ...state, messages: updatedMessages };
        }
      }
      return state; // Should not happen if logic is correct
    }
    
    case 'assistant_pdf_answer':
      return {
        ...state,
        interimTranscript: '',
        messages: [
          ...state.messages,
          {
            role: action.role,
            type: 'pdf_answer',
            content: action.content,
            source: action.source,
            source_location: action.source_location,
            sources: action.sources, // Store the sources array for UI
            timestamp: new Date().toISOString(),
            messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          },
        ],
      };
      
    // Add conversation history management
    case 'load_conversation_history': {
      return {
        ...state,
        messages: action.messages || [],
        conversationId: action.conversationId,
        sessionStartTime: action.sessionStartTime
      };
    }
    
    case 'add_conversation_context': {
      return {
        ...state,
        conversationContext: action.context
      };
    }
    
    default: {
      return state;
    }
  }
}

export default conversationReducer;