'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import unnanuLogo from 'public/unnanu_logo.jpeg';
import micIcon from 'public/mic.svg';
import uploadIcon from 'public/upload.svg';
import bookIcon from 'public/book.svg';
import pmcsLogo from 'public/pmcs-logo.svg';

function WelcomePage() {
  const [isHovered, setIsHovered] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [showGuide, setShowGuide] = useState(false);
  const router = useRouter();

  const handleGetStarted = () => {
    if (typeof window !== 'undefined') {
      // Check if we're in a static export environment (file:// or app:// protocol)
      const isStaticExport = window.location.protocol === 'file:' || window.location.protocol === 'app:';

      if (isStaticExport) {
        window.location.href = './assistant/index.html';
      } else {
        router.push('/assistant');
      }
    }
  };

  const toggleGuide = () => {
    setShowGuide(!showGuide);
    if (!showGuide) {
      setCurrentStep(0);
    }
  };

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % steps.length);
  };

  const prevStep = () => {
    setCurrentStep((prev) => (prev - 1 + steps.length) % steps.length);
  };

  const steps = [
    {
      number: 1,
      title: "Upload Your PDF",
      description: "Click the upload button or drag and drop your PDF document. The system supports files up to 2MB for optimal performance.",
      icon: uploadIcon,
      color: "bg-blue-500",
      details: [
        "Supported format: PDF files only",
        "Recommended size: Under 2MB",
        "Documents are processed temporarily and not stored",
        "You can replace documents anytime during your session"
      ]
    },
    {
      number: 2,
      title: "Start Voice Conversation",
      description: "Say 'Hi Jessica' to begin. The system will request microphone access for voice recognition.",
      icon: micIcon,
      color: "bg-green-500",
      details: [
        "Grant microphone permissions when prompted",
        "Speak clearly and at normal volume",
        "The system supports 7 languages",
        "Real-time transcription shows what you're saying"
      ]
    },
    {
      number: 3,
      title: "Ask Questions",
      description: "Ask any question about your document. The AI will provide accurate answers with source citations.",
      icon: bookIcon,
      color: "bg-purple-500",
      details: [
        "Ask specific questions about document content",
        "Get answers with exact page references",
        "View highlighted source text in the PDF",
        "Interrupt the AI anytime by speaking (barge-in)"
      ]
    },
    {
      number: 4,
      title: "View Sources",
      description: "Click “View in PDF” to open the document in PDF format.See intelligent highlighting that shows exactly where answers come from.",
      icon: bookIcon,
      color: "bg-orange-500",
      details: [
        "Click any 'View in PDF' button in responses",
        "See highlighted text in the original document",
        "Navigate to the exact page and location",
        "Close the PDF viewer to continue conversation"
      ]
    },
    {
      number: 5,
      title: "End Session",
      description: `Say "Bye Jessica" to end the conversation with the assistant, or click the "End Conversation" button. `,
      icon: micIcon,
      color: "bg-red-500",
      details: [
        "Voice command: 'bye' or 'goodbye'",
        "Or click the 'End Conversation' button",
        "Session data is cleared when you leave",
        "Start fresh with new documents anytime"
      ]
    }
  ];

  const features = [
    {
      icon: micIcon,
      title: "Voice Conversations",
      description: "Have natural voice conversations with AI. Ask questions about your documents and get instant voice responses with human-like speech synthesis.",
      color: "bg-blue-500"
    },
    {
      icon: bookIcon,
      title: "Precise Source Highlighting",
      description: "See exactly where answers come from with intelligent text highlighting. Click to view the exact source text highlighted in your PDF documents.",
      color: "bg-purple-500"
    },
    {
      icon: uploadIcon,
      title: "Multi-Language Support",
      description: "Communicate naturally in 7 languages including English, German, French, Italian, Portuguese, Hindi, Spanish, and Thai.",
      color: "bg-green-500"
    },
    {
      icon: micIcon,
      title: "Real-time Transcription",
      description: "See your speech transcribed in real-time as you speak. Get instant transcription of what the system heard.",
      color: "bg-orange-500"
    }
  ];

  return (
    <div className='welcome-page flex flex-col min-h-screen bg-gray-900 text-white'>
      <header className='flex items-center justify-between p-2 border-b border-gray-700'>
        <div className='w-12'></div> {/* Spacer for left side */}
        
        {/* PMCS Services Logo - Center */}
        <div className='flex items-center justify-center flex-1'>
          <Image src="https://pmcsservices.com/assets/img/pmcs-logo.png" width={80} height={40} alt='PMCS Services Logo' />
        </div>
        
        {/* Powered by Unnanu - Right */}
        <div className='flex items-center gap-2 text-gray-300'>
          <span className='text-xs'>Powered by</span>
          <Image src={unnanuLogo} width={20} height={20} alt='Unnanu AI Logo' className='rounded-full' />
        </div>
      </header>

      <main className='flex-1 flex flex-col items-center justify-start p-4 bg-gray-900 overflow-auto'>
        <div className='text-center mb-8'>
          <h1 className='text-4xl font-bold text-white mb-3'>
            Welcome to <span className='text-blue-400'>Interactive Voice Assistant</span>
          </h1>
          <p className='text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed'>
            Your intelligent AI voice assistant that transforms how you interact with PDF documents. 
            Ask questions, get precise answers with source highlighting. 
          </p>
        </div>

        {/* Step-by-Step Guide Section */}
        {showGuide ? (
          <div className='w-full max-w-3xl mb-6 px-4 mt-4'>
            <div className='bg-gray-800 rounded-xl p-3 md:p-4 pb-6 border border-gray-700'>
              <div className='flex items-center justify-between mb-3 md:mb-4'>
                <h2 className='text-lg md:text-xl font-bold text-white'>How to Use the Voice Assistant</h2>
                <button
                  onClick={toggleGuide}
                  className='text-gray-400 hover:text-white transition-colors'
                >
                  <svg className='w-5 h-5' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M6 18L18 6M6 6l12 12' />
                  </svg>
                </button>
              </div>

              {/* Progress Indicator */}
              <div className='flex justify-center mb-4 md:mb-6 overflow-x-auto py-3'>
                <div className='flex space-x-3 md:space-x-4 min-w-max items-center'>
                  {steps.map((step, index) => (
                    <div key={index} className='flex items-center'>
                      <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center text-xs md:text-sm font-bold transition-all duration-300 px-2 ${
                        index === currentStep 
                          ? 'bg-blue-500 text-white scale-110' 
                          : index < currentStep 
                            ? 'bg-green-500 text-white' 
                            : 'bg-gray-600 text-gray-300'
                      }`}>
                        {index < currentStep ? '✓' : step.number}
                      </div>
                      {index < steps.length - 1 && (
                        <div className={`w-8 md:w-12 h-[3px] mx-2 md:mx-3 transition-all duration-300 ${
                          index < currentStep ? 'bg-green-500' : 'bg-gray-600'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Current Step Content */}
              <div className='text-center mb-3 md:mb-4'>
                <div className={`w-14 h-14 md:w-16 md:h-16 ${steps[currentStep].color} rounded-full flex items-center justify-center mb-2 md:mb-3 mx-auto`}>
                  <Image src={steps[currentStep].icon} width={28} height={28} alt={steps[currentStep].title} className='md:w-8 md:h-8' />
                </div>
                <h3 className='text-base md:text-lg font-bold text-white mb-2 md:mb-3'>
                  Step {steps[currentStep].number}: {steps[currentStep].title}
                </h3>
                <p className='text-gray-300 text-sm md:text-base leading-relaxed max-w-2xl mx-auto mb-2 md:mb-3 px-3'>
                  {steps[currentStep].description}
                </p>
                
                {/* Step Details */}
                <div className='bg-gray-700 rounded-lg p-2 md:p-3 max-w-2xl mx-auto'>
                  <h4 className='text-white font-semibold mb-1.5 md:mb-2'>Key Points:</h4>
                  <ul className='text-left space-y-1'>
                    {steps[currentStep].details.map((detail, index) => (
                      <li key={index} className='flex items-start space-x-2 text-gray-300 text-xs md:text-sm'>
                        <svg className='w-3 h-3 md:w-3.5 md:h-3.5 text-green-400 mt-0.5 flex-shrink-0' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                          <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
                        </svg>
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Navigation Buttons */}
              <div className='flex flex-col md:flex-row justify-between items-center space-y-3 md:space-y-0 mb-4'>
                <button
                  onClick={prevStep}
                  className='flex items-center space-x-2 px-4 md:px-5 py-2 md:py-2.5 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-300 w-full md:w-auto text-sm font-semibold tracking-wide'
                >
                  <svg className='w-4 h-4 md:w-4 md:h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
                  </svg>
                  <span>Previous</span>
                </button>
                
                <div className='text-gray-400 text-sm order-first md:order-none'>
                  {currentStep + 1} of {steps.length}
                </div>
                
                <button
                  onClick={nextStep}
                  className='flex items-center space-x-2 px-4 md:px-5 py-2 md:py-2.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-300 w-full md:w-auto text-sm font-semibold tracking-wide'
                >
                  <span>Next</span>
                  <svg className='w-4 h-4 md:w-4 md:h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                    <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
                  </svg>
                </button>
              </div>

            </div>
          </div>
        ) : (
          <div className='flex justify-center mb-8 max-w-6xl w-full'>
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 w-full'>
              {features.map((feature, index) => (
                <div 
                  key={index}
                  className='bg-gray-800 rounded-xl p-4 border border-gray-700 hover:border-blue-500 transition-all duration-300 h-full'
                >
                  <div className={`w-12 h-12 ${feature.color} rounded-full flex items-center justify-center mb-3 mx-auto`}>
                    <Image src={feature.icon} width={24} height={24} alt={feature.title} />
                  </div>
                  <h3 className='text-lg font-semibold text-white mb-2 text-center'>
                    {feature.title}
                  </h3>
                  <p className='text-gray-300 text-center leading-relaxed text-sm'>
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className='flex flex-col sm:flex-row items-center justify-center gap-3 px-4 text-center mb-8'>
          {!showGuide && (
            <button
              onClick={toggleGuide}
              className='w-full sm:w-auto max-w-64 bg-purple-500 text-white px-6 md:px-8 py-3 md:py-4 rounded-full cursor-pointer transition-all duration-300 shadow-lg hover:bg-purple-600 hover:scale-105 font-bold tracking-wide text-lg'
            >
              Step-by-Step Guide
            </button>
          )}
          
          {!showGuide && (
            <button
              className={`w-full sm:w-auto max-w-64 bg-blue-500 text-white px-6 md:px-8 py-3 md:py-4 rounded-full cursor-pointer transition-all duration-300 shadow-lg transform font-bold tracking-wide text-lg ${
                isHovered ? 'bg-blue-600 scale-105 shadow-xl' : 'hover:bg-blue-600 hover:scale-105'
              }`}
              onClick={handleGetStarted}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              Get Started
            </button>
          )}
        </div>

        {/* Disclaimers */}
        <div className='max-w-3xl mx-auto'>
          <div className='bg-yellow-900/20 border border-yellow-600/30 rounded-xl p-5'>
            <h4 className='text-yellow-400 font-bold mb-4 text-center text-lg'>Disclaimer</h4>
            <div className='space-y-3'>
              <div className='flex items-start space-x-3 text-yellow-300 text-base'>
                <svg className='w-5 h-5 mt-0.5 flex-shrink-0' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z' />
                </svg>
                <span>This PDF upload feature is provided for demonstration purposes only. For optimal performance, we recommend using files no larger than 2 MB. Any documents you upload are processed temporarily in-memory and will not be stored or used to train our models.</span>
              </div>
              {/* <div className='flex items-start space-x-2 text-yellow-300 text-sm'>
                <svg className='w-4 h-4 mt-0.5 flex-shrink-0' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                  <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z' />
                </svg>
                <span>Highlighting works best with single-page files</span>
              </div> */}
            </div>
          </div>
        </div>
      </main>

      <footer className='p-4 border-t border-gray-700 bg-gray-900'>
        <div className='text-center text-gray-400 text-sm'>
          © {new Date().getFullYear()} PMCS Services. All rights reserved.
        </div>
      </footer>
    </div>
  );
}

export default WelcomePage; 